import { Metadata } from "next";
import { LoginForm } from "@/components/auth/login-form";

export const metadata: Metadata = {
  title: "Connexion | HexaTikPay",
  description: "Connectez-vous à votre compte HexaTikPay",
};

export default function LoginPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-neutral-950 py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="relative h-12 w-12 rounded-full bg-white">
            <div className="absolute inset-2 rounded-full bg-purple-600"></div>
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-white">
          Connexion à HexaTikPay
        </h2>
        <p className="mt-2 text-center text-sm text-neutral-400">
          Accédez à votre espace personnel pour gérer vos comptes TikTok monétisés
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-neutral-900 px-4 py-8 shadow sm:rounded-lg sm:px-10 border border-neutral-800">
          <LoginForm />
        </div>
      </div>
    </div>
  );
}
