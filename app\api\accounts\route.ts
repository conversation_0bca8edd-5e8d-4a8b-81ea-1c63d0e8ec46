import { NextRequest, NextResponse } from "next/server";
import { mockAccounts } from "@/lib/mock/auth-mock";

export async function GET(request: NextRequest) {
  try {
    // Simuler un délai de réponse
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    // Récupérer les paramètres de requête
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get("userId");
    const accountId = searchParams.get("id");
    
    // Si un ID de compte est spécifié, retourner ce compte spécifique
    if (accountId) {
      const account = mockAccounts.find(acc => acc.id.toString() === accountId);
      
      if (!account) {
        return NextResponse.json(
          { error: "Compte non trouvé" },
          { status: 404 }
        );
      }
      
      return NextResponse.json(account);
    }
    
    // Sinon, retourner tous les comptes (dans un cas réel, on filtrerait par userId)
    return NextResponse.json(mockAccounts);
  } catch (error) {
    console.error("Erreur lors de la récupération des comptes:", error);
    return NextResponse.json(
      { error: "Erreur lors de la récupération des comptes" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Récupérer les données de la requête
    const data = await request.json();
    
    // Vérifier les données requises
    if (!data.type || !data.userId) {
      return NextResponse.json(
        { error: "Données incomplètes" },
        { status: 400 }
      );
    }
    
    // Simuler un délai de traitement
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Créer un nouveau compte (simulé)
    const newAccount = {
      id: mockAccounts.length + 1,
      username: data.username || `@tiktok_user${Math.floor(Math.random() * 10000)}`,
      type: data.type,
      status: "En préparation",
      followers: 0,
      views: 0,
      revenue: 0,
      createdAt: new Date().toISOString(),
      userId: data.userId,
    };
    
    // Dans un cas réel, on ajouterait ce compte à la base de données
    // mockAccounts.push(newAccount);
    
    return NextResponse.json(newAccount, { status: 201 });
  } catch (error) {
    console.error("Erreur lors de la création du compte:", error);
    return NextResponse.json(
      { error: "Erreur lors de la création du compte" },
      { status: 500 }
    );
  }
}
