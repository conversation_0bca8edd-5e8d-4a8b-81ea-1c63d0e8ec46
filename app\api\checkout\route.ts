import { NextRequest, NextResponse } from "next/server";

// Plans disponibles
const plans = {
  standard: {
    id: "plan_standard",
    name: "Standard",
    price: 10,
  },
  pro: {
    id: "plan_pro",
    name: "Pro",
    price: 49,
  },
  premium: {
    id: "plan_premium",
    name: "Premium",
    price: 199,
  },
};

// Abonnements disponibles
const subscriptions = {
  "basic-sub": {
    id: "sub_basic",
    name: "Abonnement Standard",
    price: 9.99,
    period: "month",
  },
  "premium-sub": {
    id: "sub_premium",
    name: "Abonnement Premium",
    price: 29.99,
    period: "month",
  },
  enterprise: {
    id: "sub_enterprise",
    name: "Compte Entreprise",
    price: 1500,
    period: "year",
  },
};

export async function POST(request: NextRequest) {
  try {
    // Récupérer les données de la requête
    const data = await request.json();
    
    // Vérifier les données requises
    if (!data.paymentMethod || (!data.plan && !data.subscription)) {
      return NextResponse.json(
        { error: "Données de paiement incomplètes" },
        { status: 400 }
      );
    }
    
    // Simuler un délai de traitement
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // Déterminer le produit à acheter
    let product;
    let isSubscription = false;
    
    if (data.plan) {
      product = plans[data.plan as keyof typeof plans];
    } else if (data.subscription) {
      product = subscriptions[data.subscription as keyof typeof subscriptions];
      isSubscription = true;
    }
    
    if (!product) {
      return NextResponse.json(
        { error: "Produit non trouvé" },
        { status: 404 }
      );
    }
    
    // Simuler une réponse de paiement réussie
    const paymentResponse = {
      id: `payment_${Math.random().toString(36).substring(2, 15)}`,
      amount: product.price,
      currency: "EUR",
      status: "succeeded",
      created: new Date().toISOString(),
      product: {
        id: product.id,
        name: product.name,
        price: product.price,
        ...(isSubscription && { period: product.period }),
      },
      isSubscription,
    };
    
    return NextResponse.json(paymentResponse);
  } catch (error) {
    console.error("Erreur de paiement:", error);
    return NextResponse.json(
      { error: "Erreur lors du traitement du paiement" },
      { status: 500 }
    );
  }
}
