import { Metadata } from "next";
import { notFound } from "next/navigation";
import { AccountDetails } from "@/components/dashboard/accounts/account-details";
import { mockAccounts } from "@/lib/mock/auth-mock";

export const metadata: Metadata = {
  title: "Détails du compte | HexaTikPay",
  description: "Détails et performances de votre compte TikTok",
};

export default function AccountDetailsPage({ params }: { params: { id: string } }) {
  // Récupérer l'ID du compte depuis les paramètres de l'URL
  const accountId = parseInt(params.id);
  
  // Trouver le compte correspondant dans les données mock
  const account = mockAccounts.find(acc => acc.id === accountId);
  
  // Si le compte n'existe pas, afficher une page 404
  if (!account) {
    notFound();
  }
  
  return (
    <>
      <div className="grid gap-6">
        <AccountDetails account={account} />
      </div>
    </>
  );
}
