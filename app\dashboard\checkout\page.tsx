import { Metadata } from "next";
import { redirect } from "next/navigation";
import { CheckoutForm } from "@/components/dashboard/checkout/checkout-form";
import { CheckoutSummary } from "@/components/dashboard/checkout/checkout-summary";

export const metadata: Metadata = {
  title: "Paiement | HexaTikPay",
  description: "Finaliser votre achat",
};

export default function CheckoutPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  // Récupérer le plan ou l'abonnement depuis les paramètres de recherche
  const plan = searchParams.plan as string;
  const subscription = searchParams.subscription as string;
  
  // Si aucun plan ou abonnement n'est spécifié, rediriger vers la page des formules
  if (!plan && !subscription) {
    redirect("/dashboard/plans");
  }
  
  return (
    <>
      <div className="grid gap-6">
        <div>
          <h1 className="text-3xl font-bold text-white">Paiement</h1>
          <p className="text-neutral-400 mt-2">
            Finalisez votre achat en toute sécurité
          </p>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
          <CheckoutForm />
          <CheckoutSummary plan={plan} subscription={subscription} />
        </div>
      </div>
    </>
  );
}
