import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export const metadata: Metadata = {
  title: "Paiement réussi | HexaTikPay",
  description: "Votre paiement a été traité avec succès",
};

export default function CheckoutSuccessPage() {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="h-24 w-24 rounded-full bg-green-500/20 flex items-center justify-center mb-6">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-12 w-12 text-green-500"
        >
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
          <polyline points="22 4 12 14.01 9 11.01" />
        </svg>
      </div>
      
      <h1 className="text-3xl font-bold text-white mb-2">Paiement réussi !</h1>
      <p className="text-neutral-400 max-w-md mb-8">
        Votre paiement a été traité avec succès. Vous recevrez un email de confirmation dans les prochaines minutes.
      </p>
      
      <div className="bg-neutral-900 border border-neutral-800 rounded-lg p-6 max-w-md w-full mb-8">
        <h2 className="text-xl font-bold text-white mb-4">Que se passe-t-il maintenant ?</h2>
        <ul className="space-y-4 text-left">
          <li className="flex items-start gap-3">
            <div className="h-6 w-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-purple-400 text-sm font-medium">1</span>
            </div>
            <p className="text-neutral-300">
              Notre équipe va commencer à travailler sur votre compte TikTok dès maintenant.
            </p>
          </li>
          <li className="flex items-start gap-3">
            <div className="h-6 w-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-purple-400 text-sm font-medium">2</span>
            </div>
            <p className="text-neutral-300">
              Vous recevrez une notification par email dès que votre compte sera prêt.
            </p>
          </li>
          <li className="flex items-start gap-3">
            <div className="h-6 w-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-purple-400 text-sm font-medium">3</span>
            </div>
            <p className="text-neutral-300">
              Vous pourrez suivre l'avancement de votre commande dans la section "Mes comptes".
            </p>
          </li>
        </ul>
      </div>
      
      <div className="flex gap-4">
        <Link href="/dashboard">
          <Button className="bg-purple-600 hover:bg-purple-700 text-white">
            Retour au dashboard
          </Button>
        </Link>
        <Link href="/dashboard/accounts">
          <Button variant="outline" className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800">
            Voir mes comptes
          </Button>
        </Link>
      </div>
    </div>
  );
}
