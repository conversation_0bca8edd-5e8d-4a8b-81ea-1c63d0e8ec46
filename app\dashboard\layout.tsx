import { DashboardNavbar } from "@/components/dashboard/dashboard-navbar";
import { Footer } from "@/components/layout/footer";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-white dark:bg-neutral-950 text-neutral-900 dark:text-white transition-colors duration-300">
      <DashboardNavbar />
      <main className="container mx-auto px-4 py-8 md:px-8 pt-24 pb-16">
        {children}
      </main>
      <Footer />
    </div>
  );
}
