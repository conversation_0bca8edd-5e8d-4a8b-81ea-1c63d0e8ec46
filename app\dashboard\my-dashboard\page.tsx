import { Metadata } from "next";
import { DashboardStats } from "@/components/dashboard/dashboard-stats";
import { DashboardAccounts } from "@/components/dashboard/dashboard-accounts";
import { DashboardRecommendations } from "@/components/dashboard/dashboard-recommendations";

export const metadata: Metadata = {
  title: "Mon Tableau de Bord | HexaTikPay",
  description: "Gérez vos comptes TikTok monétisés et suivez vos performances",
};

export default function MyDashboardPage() {
  return (
    <>
      <div className="grid gap-6">
        <h1 className="text-3xl font-bold text-white">Mon Tableau de Bord</h1>
        <p className="text-neutral-400">
          Gérez vos comptes TikTok monétisés et suivez vos performances en temps réel.
        </p>

        <DashboardStats />

        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 mt-6">
          <DashboardAccounts />
          <DashboardRecommendations />
        </div>
      </div>
    </>
  );
}
