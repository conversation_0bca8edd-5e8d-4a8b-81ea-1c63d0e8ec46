import { Metadata } from "next";
import { WelcomeHero } from "@/components/dashboard/welcome/welcome-hero";
import { FeatureHighlights } from "@/components/dashboard/welcome/feature-highlights";
import { PromoSpots } from "@/components/dashboard/welcome/promo-spots";
import { Testimonials } from "@/components/dashboard/welcome/testimonials";
import { QuickStats } from "@/components/dashboard/welcome/quick-stats";

export const metadata: Metadata = {
  title: "Dashboard | HexaTikPay",
  description: "Votre plateforme pour acquérir et gérer des comptes TikTok monétisés",
};

export default function DashboardPage() {
  return (
    <>
      <div className="grid gap-10">
        {/* Hero Section */}
        <WelcomeHero />

        {/* Quick Stats for Retur ning Users */}
        <QuickStats />

        {/* Feature Highlights */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Nos fonctionnalités</h2>
          <FeatureHighlights />
        </div>

        {/* Promotional Spots */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Offres spéciales</h2>
          <PromoSpots />
        </div>

        {/* Testimonials */}
        <Testimonials />

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-purple-900 to-blue-900 rounded-xl p-8 text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
            Prêt à commencer votre aventure TikTok ?
          </h2>
          <p className="text-white/80 mb-6 max-w-2xl mx-auto">
            Rejoignez des milliers de créateurs qui génèrent des revenus grâce à HexaTikPay.
            Nos comptes TikTok monétisés sont prêts à l'emploi.
          </p>
          <a
            href="/dashboard/plans"
            className="inline-block bg-white text-purple-900 px-6 py-3 rounded-lg font-medium hover:bg-white/90 transition-colors"
          >
            Voir nos formules
          </a>
        </div>
      </div>
    </>
  );
}
