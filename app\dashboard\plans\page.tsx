import { Metadata } from "next";
import { PlanCards } from "@/components/dashboard/plan-cards";
import { SubscriptionCards } from "@/components/dashboard/subscription-cards";

export const metadata: Metadata = {
  title: "Formules | HexaTikPay",
  description: "Choisissez la formule qui correspond à vos besoins",
};

export default function PlansPage() {
  return (
    <>
      <div className="grid gap-6">
        <div>
          <h1 className="text-3xl font-bold text-white">Formules</h1>
          <p className="text-neutral-400 mt-2">
            Choisissez la formule qui correspond à vos besoins pour obtenir un compte TikTok monétisé.
          </p>
        </div>
        
        <div className="mt-6">
          <h2 className="text-xl font-semibold text-white mb-4">Formules principales</h2>
          <PlanCards />
        </div>
        
        <div className="mt-10">
          <h2 className="text-xl font-semibold text-white mb-4">Abonnements</h2>
          <p className="text-neutral-400 mb-6">
            Souscrivez à un abonnement pour bénéficier d'une réduction jusqu'à 20% sur vos formules.
          </p>
          <SubscriptionCards />
        </div>
      </div>
    </>
  );
}
