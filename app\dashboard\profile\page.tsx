import { Metadata } from "next";
import { ProfileHeader } from "@/components/dashboard/profile/profile-header";
import { ProfileForm } from "@/components/dashboard/profile/profile-form";
import { PaymentMethods } from "@/components/dashboard/profile/payment-methods";
import { SecuritySettings } from "@/components/dashboard/profile/security-settings";

export const metadata: Metadata = {
  title: "Profil | HexaTikPay",
  description: "Gérez votre profil et vos paramètres",
};

export default function ProfilePage() {
  return (
    <>
      <div className="grid gap-6">
        <ProfileHeader />
        <div className="grid gap-6 md:grid-cols-2">
          <div className="space-y-6">
            <ProfileForm />
            <SecuritySettings />
          </div>
          <div>
            <PaymentMethods />
          </div>
        </div>
      </div>
    </>
  );
}
