import { Metadata } from "next";
import { SubscriptionCards } from "@/components/dashboard/subscription-cards";
import { SubscriptionHeader } from "@/components/dashboard/subscriptions/subscription-header";
import { CurrentSubscription } from "@/components/dashboard/subscriptions/current-subscription";

export const metadata: Metadata = {
  title: "Abonnements | HexaTikPay",
  description: "Gérez vos abonnements et bénéficiez de réductions sur vos formules",
};

export default function SubscriptionsPage() {
  return (
    <>
      <div className="grid gap-6">
        <SubscriptionHeader />
        <CurrentSubscription />
        <div className="mt-10">
          <h2 className="text-xl font-semibold text-white mb-6">Changer d'abonnement</h2>
          <SubscriptionCards />
        </div>
      </div>
    </>
  );
}
