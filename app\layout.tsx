import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "HexaTikPay - AI-Powered Crypto Insights",
  description: "Trade smarter with AI-powered crypto insights. Track your assets and maximize your crypto investments with precision and ease.",
  keywords: ["crypto", "trading", "AI", "blockchain", "investment", "finance"],
};

// Importation dynamique des providers
import dynamic from 'next/dynamic';
const AuthProviderClient = dynamic(() => import('@/lib/context/auth-context').then(mod => mod.AuthProvider));
const ThemeProvider = dynamic(() => import('@/components/theme/theme-provider').then(mod => mod.ThemeProvider));

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange={false}
        >
          <AuthProviderClient>
            {children}
          </AuthProviderClient>
        </ThemeProvider>
      </body>
    </html>
  );
}
