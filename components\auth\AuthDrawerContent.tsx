
'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { signInAction, signUpAction } from "@/lib/actions/auth"
import { FormMessage, Message } from "@/components/auth/form-message"
import { GoogleButton } from "@/components/auth/google-button"
import { SubmitButton } from "@/components/auth/submit-button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
} from "@/components/ui/drawer"

interface AuthDrawerContentProps {
  initialMode?: 'login' | 'register';
  onClose?: () => void;
}

// Animation variants for a subtle, staggered entrance
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 15 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
    },
  },
}

export const AuthDrawerContent: React.FC<AuthDrawerContentProps> = ({
  initialMode = 'login',
  onClose,
}) => {
  const [mode, setMode] = useState<'login' | 'register'>(initialMode)
  const [message, setMessage] = useState<Message | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Unified handler for form actions
  const handleFormAction = async (
    action: (formData: FormData) => Promise<Message | void | null | undefined>,
    formData: FormData
  ) => {
    setIsSubmitting(true)
    setMessage(null) // Clear previous messages

    try {
      const result = await action(formData)

      if (result) {
        setMessage(result)
        if ('success' in result && result.success && onClose) {
            // Delay closing on success for message visibility and smoother UX
            setTimeout(() => {
              if (onClose) onClose()
            }, 'error' in result ? 3000 : 1500); // Longer if error, shorter if success and closing
        }
      } else if (!result && onClose) { // Assumed success if no message and onClose is present
        setTimeout(() => {
          if (onClose) onClose()
        }, 300)
      }
    } catch (error) {
      console.error(`${mode} action error:`, error)
      setMessage({ error: "Une erreur inattendue est survenue. Veuillez réessayer." })
    } finally {
      // Only set isSubmitting to false if there's an error or no success message that closes the drawer
      // If successful and drawer closes, no need to re-enable.
      // However, if an action might return a success message *without* closing, this needs adjustment.
      // For now, assume success leads to close or displays a message then allows re-submission.
      if ((message && 'error' in message) || !(message && 'success' in message && message.success && onClose)) {
        setIsSubmitting(false)
      }
    }
  }

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    const formData = new FormData(event.currentTarget)
    const currentAction = mode === 'login' ? signInAction : signUpAction
    await handleFormAction(currentAction as any, formData) // Cast if action signature needs it
  }

  const title = mode === 'login' ? 'Se Connecter' : "S'inscrire"
  const description = mode === 'login'
    ? "Accédez à votre espace personnel Hexa Chat."
    : "Rejoignez la communauté Hexa Chat."

  const submitButtonText = mode === 'login' ? 'Se Connecter' : "S'inscrire"
  const pendingSubmitButtonText = mode === 'login' ? "Connexion..." : "Inscription..."

  const toggleModeText = mode === 'login' ? "Pas encore de compte ?" : "Déjà un compte ?"
  const toggleModeButtonText = mode === 'login' ? "S'inscrire" : "Se Connecter"

  return (
    <motion.div
      className="p-6 pb-0 text-foreground  bg-neutral-900 rounded-t-lg md:rounded-lg" // Assuming drawer bg is neutral-900
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <DrawerHeader className="text-left p-0 mb-6">
        <motion.div variants={itemVariants}>
          <DrawerTitle className="text-2xl font-bold text-white mb-1.5">
            {title}
          </DrawerTitle>
        </motion.div>
        <motion.div variants={itemVariants}>
          <DrawerDescription className="text-neutral-300 text-base">
            {description}
          </DrawerDescription>
        </motion.div>
      </DrawerHeader>

      <form
        onSubmit={handleSubmit}
        className="flex flex-col gap-5" // Slightly reduced gap for tighter feel
      >
        <motion.div variants={itemVariants} className="space-y-2">
          <Label htmlFor="email" className="text-neutral-200 font-medium text-sm">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            required
            disabled={isSubmitting}
            // Refined input styling for a softer, more integrated look
            className="bg-neutral-900/70 border-neutral-700/90 text-white placeholder-neutral-500 focus:ring-2 focus:ring-purple-500/70 focus:border-purple-600 h-12 rounded-lg transition-all duration-200 ease-in-out shadow-sm"
          />
        </motion.div>
        <motion.div variants={itemVariants} className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="password" className="text-neutral-200 font-medium text-sm">Mot de passe</Label>
            {mode === 'login' && (
              <a
                href="/forgot-password" // Consider making this a button for better semantics if it triggers an in-app action
                className="text-xs text-purple-400 hover:text-purple-300 transition-colors duration-150"
              >
                Mot de passe oublié ?
              </a>
            )}
          </div>
          <Input
            id="password"
            type="password"
            name="password"
            placeholder="••••••••"
            required
            disabled={isSubmitting}
            className="bg-neutral-900/70 border-neutral-700/90 text-white placeholder-neutral-500 focus:ring-2 focus:ring-purple-500/70 focus:border-purple-600 h-12 rounded-lg transition-all duration-200 ease-in-out shadow-sm"
          />
        </motion.div>

        {message && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="my-1" // Added margin for better spacing around the message
          >
            <FormMessage message={message} />
          </motion.div>
        )}

        <motion.div variants={itemVariants} className="mt-2"> {/* Added slight margin-top */}
          <SubmitButton
            pendingText={pendingSubmitButtonText}
            isPending={isSubmitting}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white font-semibold py-3 rounded-lg transition-all duration-300 ease-in-out shadow-lg hover:shadow-purple-500/30 h-12 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-neutral-900"
          >
            {submitButtonText}
          </SubmitButton>
        </motion.div>
      </form>

      <motion.div variants={itemVariants} className="relative my-6"> {/* Adjusted margin */}
        <div className="absolute inset-0 flex items-center" aria-hidden="true">
          <span className="w-full border-t border-neutral-700/80" /> {/* Slightly softer border */}
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-neutral-900 px-3 text-neutral-400 font-medium tracking-wide"> {/* Improved contrast & style */}
            ou continuer avec
          </span>
        </div>
      </motion.div>

      <motion.div variants={itemVariants}>
        <GoogleButton
          mode={mode === 'login' ? 'sign-in' : 'sign-up'}
          disabled={isSubmitting}
          // Slightly refined Google button style
          className="w-full bg-neutral-800 hover:bg-neutral-700/80 border border-neutral-700 text-neutral-100 h-12 rounded-lg transition-all duration-200 ease-in-out shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2 focus:ring-offset-neutral-900"
        />
      </motion.div>

      <DrawerFooter className="pt-6 px-0 pb-6"> {/* Adjusted padding */}
        <motion.p variants={itemVariants} className="text-center text-sm text-neutral-400">
          {toggleModeText}
          <button
            type="button"
            onClick={() => {
              if (isSubmitting) return;
              setMode(mode === 'login' ? 'register' : 'login');
              setMessage(null); // Clear messages when mode changes
            }}
            disabled={isSubmitting}
            className="font-medium text-purple-400 hover:text-purple-300 transition-colors duration-150 ml-1 focus:outline-none focus:underline"
          >
            {toggleModeButtonText}
          </button>
        </motion.p>
      </DrawerFooter>
    </motion.div>
  )
}

export default AuthDrawerContent
