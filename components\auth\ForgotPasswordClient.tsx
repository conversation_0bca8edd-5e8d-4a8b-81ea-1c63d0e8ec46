'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { forgotPasswordAction } from "@/lib/actions/auth"
import { FormMessage, Message } from "@/components/auth/form-message"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ButtonRipple } from '@/components/auth/ButtonRipple'
import { useFormStatus } from 'react-dom'

// Background component similar to HeroBackground in classy-hero.tsx
const ForgotPasswordBackground = () => {
  return (
    <div className="absolute inset-0 -z-10 overflow-hidden bg-neutral-950">
      <motion.div
        className="absolute inset-0"
        style={{ backgroundImage: `radial-gradient(circle, rgba(255,255,255,0.03) 0.8px, transparent 0.8px)`, backgroundSize: '30px 30px' }}
        animate={{ backgroundPosition: ['0 0', '30px 30px'] }}
        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="absolute inset-0"
        style={{ background: `radial-gradient(ellipse at center, rgba(168, 85, 247, 0.1) 0%, transparent 55%)` }}
        animate={{ opacity: [0.6, 0.9, 0.6] }}
        transition={{ duration: 10, repeat: Infinity, repeatType: "reverse" }}
      />
      <motion.div
        className="absolute inset-0"
        style={{ background: `radial-gradient(ellipse at 70% 30%, rgba(236, 72, 153, 0.07) 0%, transparent 50%)` }}
        animate={{ opacity: [0.4, 0.7, 0.4], scale: [1, 1.05, 1] }}
        transition={{ duration: 13, repeat: Infinity, repeatType: "reverse", delay: 2.5 }}
      />
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-neutral-700/50 to-transparent opacity-30" />
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-neutral-800/40 to-transparent opacity-20" />
      </div>
    </div>
  )
}

// Navbar component
const Navbar = () => {
  return (
    <motion.nav
      initial={{ opacity: 0, y: -25 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
      className="fixed top-0 left-0 right-0 z-50 w-full py-3 md:py-4 px-4 sm:px-6 lg:px-10 transition-all duration-300 bg-neutral-950/80 backdrop-blur-lg border-b border-neutral-800"
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between font-plus-jakarta border-2 border-purple-600 p-1 rounded-4xl">
        <motion.div
          initial={{ x: -20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <Link href="/" className="flex items-center group">
            <div className="relative w-10 h-10 sm:w-12 sm:h-12">
              <Image
                src="https://res.cloudinary.com/dpqhqrs7k/image/upload/v1746595081/ChatGPT_Image_May_7_2025_12_33_53_AM_ngtoat.png"
                alt="Hexa AI Logo"
                fill
                style={{ objectFit: 'contain' }}
                priority
                sizes="(max-width: 640px) 40px, 48px"
              />
            </div>
            <span className="text-white relative text-lg sm:text-xl md:text-2xl font-bold ml-2 group-hover:text-purple-400 transition-colors">Hexa AI</span>
          </Link>
        </motion.div>
      </div>
    </motion.nav>
  )
}

// Custom submit button with ripple effect
function SubmitRippleButton() {
  const { pending } = useFormStatus()
  
  return (
    <ButtonRipple
      type="submit"
      disabled={pending}
      className="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white font-semibold py-3 rounded-lg transition-all duration-300 shadow-md hover:shadow-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-neutral-900 h-12 mt-4 disabled:opacity-70 disabled:cursor-not-allowed"
      rippleColor="rgba(255, 255, 255, 0.3)"
    >
      {pending ? "Envoi en cours..." : "Réinitialiser le mot de passe"}
    </ButtonRipple>
  )
}

// Main component
export default function ForgotPasswordClient({ searchParams }: { searchParams: any }) {
  const [message, setMessage] = useState<Message | null>(
    searchParams?.error
      ? { error: searchParams.error }
      : searchParams?.success
      ? { success: searchParams.success }
      : null
  )

  return (
    <div className="relative min-h-screen w-full flex flex-col items-center justify-center overflow-hidden pt-28 pb-16 sm:pt-24 sm:pb-12 px-4 isolate">
      <ForgotPasswordBackground />
      <Navbar />
      
      <motion.div
        className="bg-neutral-900/60 backdrop-blur-lg rounded-xl overflow-hidden shadow-xl w-full max-w-md relative p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.2, ease: "easeOut" }}
      >
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h1 className="text-2xl font-bold text-white mb-2">Mot de passe oublié</h1>
          <p className="text-neutral-300 text-base mb-6">
            Entrez votre adresse e-mail pour recevoir un lien de réinitialisation
          </p>
        </motion.div>

        <form action={forgotPasswordAction} className="flex flex-col gap-5">
          <div className="space-y-2.5">
            <Label htmlFor="email" className="text-neutral-200 font-medium text-sm">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              required
              className="bg-neutral-800/80 border-neutral-700 text-white placeholder-neutral-500 focus:ring-purple-500 focus:border-purple-500 h-12 rounded-lg px-4 transition-all duration-200"
            />
          </div>

          {message && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <FormMessage message={message} />
            </motion.div>
          )}

          <SubmitRippleButton />
          
          <div className="mt-4 text-center">
            <Link 
              href="/" 
              className="text-sm text-purple-400 hover:text-purple-300 transition-colors"
            >
              Retour à l'accueil
            </Link>
          </div>
        </form>
      </motion.div>
    </div>
  )
}
