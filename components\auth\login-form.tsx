"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/lib/context/auth-context";

export function LoginForm() {
  const router = useRouter();
  const { signIn } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await signIn(formData.email, formData.password);
      router.push("/dashboard");
    } catch (err) {
      console.error("Login error:", err);
      setError("Une erreur est survenue lors de la connexion. Veuillez réessayer.");
    } finally {
      setIsLoading(false);
    }
  };

  // Pour faciliter le développement, pré-remplir avec des valeurs par défaut
  const handleDemoLogin = () => {
    setFormData({
      email: "<EMAIL>",
      password: "password123",
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <Label htmlFor="email" className="block text-sm font-medium text-neutral-200">
          Adresse email
        </Label>
        <div className="mt-1">
          <Input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={formData.email}
            onChange={handleChange}
            className="bg-neutral-800 border-neutral-700 text-white"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="password" className="block text-sm font-medium text-neutral-200">
          Mot de passe
        </Label>
        <div className="mt-1">
          <Input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            value={formData.password}
            onChange={handleChange}
            className="bg-neutral-800 border-neutral-700 text-white"
          />
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            className="h-4 w-4 rounded border-neutral-700 bg-neutral-800 text-purple-600 focus:ring-purple-500"
          />
          <Label htmlFor="remember-me" className="ml-2 block text-sm text-neutral-400">
            Se souvenir de moi
          </Label>
        </div>

        <div className="text-sm">
          <Link href="/forgot-password" className="font-medium text-purple-400 hover:text-purple-300">
            Mot de passe oublié ?
          </Link>
        </div>
      </div>

      {error && (
        <div className="rounded-md bg-red-500/20 p-4">
          <div className="flex">
            <div className="text-sm text-red-400">{error}</div>
          </div>
        </div>
      )}

      <div>
        <Button
          type="submit"
          disabled={isLoading}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white"
        >
          {isLoading ? "Connexion en cours..." : "Se connecter"}
        </Button>
      </div>

      <div className="text-center">
        <Button
          type="button"
          variant="link"
          onClick={handleDemoLogin}
          className="text-purple-400 hover:text-purple-300"
        >
          Remplir avec les données de démo
        </Button>
      </div>

      <div className="text-center text-sm text-neutral-400">
        Pas encore de compte ?{" "}
        <Link href="/register" className="font-medium text-purple-400 hover:text-purple-300">
          S'inscrire
        </Link>
      </div>
    </form>
  );
}
