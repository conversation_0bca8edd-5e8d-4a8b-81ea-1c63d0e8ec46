"use client";

import { But<PERSON> } from "@/components/ui/button";
import { type ComponentProps } from "react";
import { useFormStatus } from "react-dom";

type Props = ComponentProps<typeof Button> & {
  pendingText?: string;
  isPending?: boolean;
};

export function SubmitButton({
  children,
  pendingText = "Submitting...",
  isPending,
  ...props
}: Props) {
  const { pending: formPending } = useFormStatus();
  // Use isPending prop if provided, otherwise use the form status
  const pending = isPending !== undefined ? isPending : formPending;

  return (
    <Button type="submit" aria-disabled={pending} disabled={pending} {...props}>
      {pending ? pendingText : children}
    </Button>
  );
}
