"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer 
} from "recharts";

// Données mock pour le graphique
const performanceData = [
  { date: "2023-01-01", followers: 1000, views: 5000, revenue: 50 },
  { date: "2023-02-01", followers: 2000, views: 10000, revenue: 100 },
  { date: "2023-03-01", followers: 3000, views: 15000, revenue: 150 },
  { date: "2023-04-01", followers: 5000, views: 25000, revenue: 200 },
  { date: "2023-05-01", followers: 8000, views: 40000, revenue: 300 },
  { date: "2023-06-01", followers: 10000, views: 60000, revenue: 350 },
  { date: "2023-07-01", followers: 12500, views: 80000, revenue: 400 },
];

export function AccountDetails({ account }: { account: any }) {
  return (
    <>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <div className="flex items-center gap-2">
            <Link href="/dashboard/accounts">
              <Button variant="link" className="p-0 h-auto text-neutral-400 hover:text-white">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4 mr-1"
                >
                  <path d="m15 18-6-6 6-6" />
                </svg>
                Retour
              </Button>
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-white mt-2">{account.username}</h1>
          <div className="flex items-center gap-2 mt-1">
            <div className={`text-xs px-2 py-0.5 rounded-full ${
              account.type === "Premium" 
                ? "bg-purple-500/20 text-purple-400" 
                : account.type === "Pro"
                ? "bg-blue-500/20 text-blue-400"
                : "bg-green-500/20 text-green-400"
            }`}>
              {account.type}
            </div>
            <span className={`text-xs ${
              account.status === "Actif" 
                ? "text-green-500" 
                : "text-yellow-500"
            }`}>
              {account.status}
            </span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800">
            Modifier
          </Button>
          <Button className="bg-purple-600 hover:bg-purple-700 text-white">
            Optimiser
          </Button>
        </div>
      </div>
      
      {account.status === "Actif" ? (
        <>
          <div className="grid gap-6 md:grid-cols-3 mt-6">
            <Card className="bg-neutral-900 border-neutral-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-400">Abonnés</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{account.followers.toLocaleString()}</div>
                <p className="text-xs text-green-500 mt-1">+15% ce mois-ci</p>
              </CardContent>
            </Card>
            
            <Card className="bg-neutral-900 border-neutral-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-400">Vues</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{account.views.toLocaleString()}</div>
                <p className="text-xs text-green-500 mt-1">+22% ce mois-ci</p>
              </CardContent>
            </Card>
            
            <Card className="bg-neutral-900 border-neutral-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-neutral-400">Revenus</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{account.revenue} €</div>
                <p className="text-xs text-green-500 mt-1">+18% ce mois-ci</p>
              </CardContent>
            </Card>
          </div>
          
          <Card className="bg-neutral-900 border-neutral-800 mt-6">
            <CardHeader>
              <CardTitle className="text-white">Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={performanceData}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <defs>
                      <linearGradient id="colorFollowers" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                      </linearGradient>
                      <linearGradient id="colorViews" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#82ca9d" stopOpacity={0} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                    <XAxis 
                      dataKey="date" 
                      tick={{ fill: '#999' }} 
                      axisLine={{ stroke: '#333' }} 
                    />
                    <YAxis 
                      tick={{ fill: '#999' }} 
                      axisLine={{ stroke: '#333' }} 
                    />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#222', 
                        borderColor: '#333',
                        color: '#fff' 
                      }} 
                    />
                    <Area
                      type="monotone"
                      dataKey="followers"
                      stroke="#8884d8"
                      fillOpacity={1}
                      fill="url(#colorFollowers)"
                      name="Abonnés"
                    />
                    <Area
                      type="monotone"
                      dataKey="views"
                      stroke="#82ca9d"
                      fillOpacity={1}
                      fill="url(#colorViews)"
                      name="Vues"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </>
      ) : (
        <Card className="bg-neutral-900 border-neutral-800 mt-6">
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500/20 text-yellow-500 mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-8 w-8"
                >
                  <path d="M12 9v4" />
                  <path d="M12 17h.01" />
                  <path d="M3.34 17a10 10 0 1 1 17.32 0" />
                </svg>
              </div>
              <h2 className="text-xl font-bold text-white mb-2">Compte en préparation</h2>
              <p className="text-neutral-400 max-w-md mx-auto mb-6">
                Votre compte est en cours de création. Nous vous notifierons dès qu'il sera prêt à être utilisé.
              </p>
              <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                Vérifier le statut
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}
