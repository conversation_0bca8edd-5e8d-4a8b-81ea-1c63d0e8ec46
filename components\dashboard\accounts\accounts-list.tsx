"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { mockAccounts } from "@/lib/mock/auth-mock";

export function AccountsList() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {mockAccounts.map((account) => (
        <Card key={account.id} className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-white">{account.username}</CardTitle>
            <div className={`text-xs px-2 py-0.5 rounded-full ${
              account.type === "Premium" 
                ? "bg-purple-500/20 text-purple-400" 
                : account.type === "Pro"
                ? "bg-blue-500/20 text-blue-400"
                : "bg-green-500/20 text-green-400"
            }`}>
              {account.type}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-neutral-400">Statut</span>
              <span className={`text-sm ${
                account.status === "Actif" 
                  ? "text-green-500" 
                  : "text-yellow-500"
              }`}>
                {account.status}
              </span>
            </div>
            
            {account.status === "Actif" && (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-neutral-400">Abonnés</span>
                  <span className="text-white">{account.followers.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-neutral-400">Vues</span>
                  <span className="text-white">{account.views.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-neutral-400">Revenus</span>
                  <span className="text-white">{account.revenue} €</span>
                </div>
              </>
            )}
            
            <div className="pt-4">
              <Link href={`/dashboard/accounts/${account.id}`}>
                <Button 
                  variant="outline" 
                  className="w-full border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
                >
                  Voir les détails
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
