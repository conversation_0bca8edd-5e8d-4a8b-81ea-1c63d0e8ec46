"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export function CheckoutForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    cardNumber: "",
    cardName: "",
    expiry: "",
    cvc: "",
  });
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // Formatage spécifique pour certains champs
    if (name === "cardNumber") {
      // Formater le numéro de carte avec des espaces tous les 4 chiffres
      const formatted = value.replace(/\s/g, "").replace(/(\d{4})/g, "$1 ").trim();
      setFormData((prev) => ({ ...prev, [name]: formatted }));
    } else if (name === "expiry") {
      // Formater la date d'expiration (MM/YY)
      const formatted = value
        .replace(/\D/g, "")
        .replace(/(\d{2})(\d)/, "$1/$2")
        .substring(0, 5);
      setFormData((prev) => ({ ...prev, [name]: formatted }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };
  
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simuler le traitement du paiement
    setTimeout(() => {
      setIsLoading(false);
      router.push("/dashboard/checkout/success");
    }, 2000);
  };
  
  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">Informations de paiement</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="cardName" className="text-neutral-400">Nom sur la carte</Label>
            <Input
              id="cardName"
              name="cardName"
              value={formData.cardName}
              onChange={handleChange}
              placeholder="John Doe"
              required
              className="bg-neutral-800 border-neutral-700 text-white mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="cardNumber" className="text-neutral-400">Numéro de carte</Label>
            <Input
              id="cardNumber"
              name="cardNumber"
              value={formData.cardNumber}
              onChange={handleChange}
              placeholder="4242 4242 4242 4242"
              maxLength={19}
              required
              className="bg-neutral-800 border-neutral-700 text-white mt-1"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="expiry" className="text-neutral-400">Date d'expiration</Label>
              <Input
                id="expiry"
                name="expiry"
                value={formData.expiry}
                onChange={handleChange}
                placeholder="MM/YY"
                maxLength={5}
                required
                className="bg-neutral-800 border-neutral-700 text-white mt-1"
              />
            </div>
            <div>
              <Label htmlFor="cvc" className="text-neutral-400">CVC</Label>
              <Input
                id="cvc"
                name="cvc"
                value={formData.cvc}
                onChange={handleChange}
                placeholder="123"
                maxLength={3}
                required
                className="bg-neutral-800 border-neutral-700 text-white mt-1"
              />
            </div>
          </div>
          
          <div className="pt-4">
            <Button 
              type="submit" 
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              disabled={isLoading}
            >
              {isLoading ? "Traitement en cours..." : "Payer maintenant"}
            </Button>
          </div>
          
          <div className="text-center text-xs text-neutral-400 pt-2">
            <p>Vos informations de paiement sont sécurisées et cryptées.</p>
            <div className="flex items-center justify-center gap-2 mt-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                <path d="M7 11V7a5 5 0 0 1 10 0v4" />
              </svg>
              <span>Paiement sécurisé</span>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
