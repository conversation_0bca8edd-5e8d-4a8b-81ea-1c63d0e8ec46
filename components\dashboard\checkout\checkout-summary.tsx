"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

// Données des plans
const plans = {
  standard: {
    name: "Standard",
    price: 10,
    description: "Compte pré-créé avec un nom d'utilisateur aléatoire",
  },
  pro: {
    name: "Pro",
    price: 49,
    description: "Choix du nom d'utilisateur et pays d'origine",
  },
  premium: {
    name: "Premium",
    price: 199,
    description: "Tout ce qu'il y a dans Pro, plus création de logo et optimisation bio",
  },
};

// Données des abonnements
const subscriptions = {
  "basic-sub": {
    name: "Abonnement Standard",
    price: 9.99,
    period: "mois",
    description: "10% de réduction sur toutes les formules",
  },
  "premium-sub": {
    name: "Abonnement Premium",
    price: 29.99,
    period: "mois",
    description: "20% de réduction sur toutes les formules",
  },
  enterprise: {
    name: "Compte Entreprise",
    price: 1500,
    period: "an",
    description: "Partenariat exclusif et accès à l'API",
  },
};

export function CheckoutSummary({ 
  plan, 
  subscription 
}: { 
  plan?: string; 
  subscription?: string;
}) {
  // Déterminer le produit à afficher
  const product = plan 
    ? plans[plan as keyof typeof plans]
    : subscription 
      ? subscriptions[subscription as keyof typeof subscriptions]
      : null;
  
  if (!product) {
    return (
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white">Récapitulatif</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-neutral-400">Aucun produit sélectionné</p>
        </CardContent>
      </Card>
    );
  }
  
  // Calculer le total
  const subtotal = product.price;
  const tax = subtotal * 0.2; // TVA à 20%
  const total = subtotal + tax;
  
  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">Récapitulatif</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-white">{product.name}</h3>
            <p className="text-sm text-neutral-400">{product.description}</p>
          </div>
          <div className="text-right">
            <div className="font-medium text-white">
              {product.price} €
              {subscription && <span className="text-sm text-neutral-400">/{product.period}</span>}
            </div>
          </div>
        </div>
        
        <div className="border-t border-neutral-800 pt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-neutral-400">Sous-total</span>
            <span className="text-white">{subtotal.toFixed(2)} €</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-neutral-400">TVA (20%)</span>
            <span className="text-white">{tax.toFixed(2)} €</span>
          </div>
        </div>
        
        <div className="border-t border-neutral-800 pt-4">
          <div className="flex justify-between">
            <span className="font-medium text-white">Total</span>
            <span className="font-medium text-white">{total.toFixed(2)} €</span>
          </div>
          {subscription && (
            <p className="text-xs text-neutral-400 mt-1">
              Facturation récurrente {product.period === "mois" ? "mensuelle" : "annuelle"}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
