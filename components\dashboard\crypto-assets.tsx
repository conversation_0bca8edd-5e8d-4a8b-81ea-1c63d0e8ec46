"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";

const assets = [
  {
    id: 1,
    name: "Bitcoin",
    symbol: "BTC",
    amount: 0.45,
    value: 18245.67,
    change: 2.5,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-4 w-4 text-orange-500"
      >
        <path d="M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727" />
      </svg>
    ),
  },
  {
    id: 2,
    name: "Ethereum",
    symbol: "ETH",
    amount: 3.2,
    value: 6789.45,
    change: -1.2,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-4 w-4 text-purple-500"
      >
        <path d="m6 12 6-9 6 9M6 12l6 9 6-9M6 12h12" />
      </svg>
    ),
  },
  {
    id: 3,
    name: "Solana",
    symbol: "SOL",
    amount: 45.6,
    value: 4532.12,
    change: 5.7,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-4 w-4 text-green-500"
      >
        <rect width="20" height="12" x="2" y="6" rx="2" />
        <path d="M14 2v4M10 2v4M14 18v4M10 18v4" />
      </svg>
    ),
  },
  {
    id: 4,
    name: "Cardano",
    symbol: "ADA",
    amount: 1250,
    value: 1875.34,
    change: 0.8,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-4 w-4 text-blue-500"
      >
        <circle cx="12" cy="12" r="10" />
        <path d="M12 8v4M12 16h.01" />
      </svg>
    ),
  },
];

export function CryptoAssets() {
  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-white">Crypto Assets</CardTitle>
        <button className="text-xs text-purple-400 hover:text-purple-300">
          View All
        </button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {assets.map((asset) => (
            <div
              key={asset.id}
              className="flex items-center justify-between border-b border-neutral-800 pb-4 last:border-0 last:pb-0"
            >
              <div className="flex items-center gap-3">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-neutral-800">
                  {asset.icon}
                </div>
                <div>
                  <div className="font-medium text-white">{asset.name}</div>
                  <div className="text-xs text-neutral-400">{asset.symbol}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-white">
                  ${asset.value.toLocaleString()}
                </div>
                <div className="flex items-center justify-end gap-1">
                  <span
                    className={`text-xs ${
                      asset.change >= 0 ? "text-green-500" : "text-red-500"
                    }`}
                  >
                    {asset.change >= 0 ? "+" : ""}
                    {asset.change}%
                  </span>
                  <span className="text-xs text-neutral-400">
                    {asset.amount} {asset.symbol}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
