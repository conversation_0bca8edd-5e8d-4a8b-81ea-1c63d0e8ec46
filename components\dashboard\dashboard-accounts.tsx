"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { mockAccounts } from "@/lib/mock/auth-mock";

export function DashboardAccounts() {
  return (
    <Card className="col-span-1 lg:col-span-1 bg-neutral-900 border-neutral-800">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-white">Mes comptes TikTok</CardTitle>
        <Link href="/dashboard/accounts">
          <Button variant="link" className="text-purple-400 hover:text-purple-300 p-0">
            Voir tout
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockAccounts.map((account) => (
            <div
              key={account.id}
              className="flex items-center justify-between border-b border-neutral-800 pb-4 last:border-0 last:pb-0"
            >
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-neutral-800 text-white">
                  {account.username.charAt(1).toUpperCase()}
                </div>
                <div>
                  <div className="font-medium text-white">{account.username}</div>
                  <div className="flex items-center gap-2">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      account.type === "Premium"
                        ? "bg-purple-500/20 text-purple-400"
                        : account.type === "Pro"
                        ? "bg-blue-500/20 text-blue-400"
                        : "bg-green-500/20 text-green-400"
                    }`}>
                      {account.type}
                    </span>
                    <span className={`text-xs ${
                      account.status === "Actif"
                        ? "text-green-500"
                        : "text-yellow-500"
                    }`}>
                      {account.status}
                    </span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                {account.status === "Actif" ? (
                  <>
                    <div className="font-medium text-white">
                      {account.revenue} €
                    </div>
                    <div className="text-xs text-neutral-400">
                      {account.followers.toLocaleString()} abonnés
                    </div>
                  </>
                ) : (
                  <Button size="sm" variant="outline" className="border-purple-600 text-purple-400 hover:bg-purple-600/20">
                    Détails
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
        <div className="mt-4">
          <Link href="/dashboard/plans">
            <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
              Ajouter un compte
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
