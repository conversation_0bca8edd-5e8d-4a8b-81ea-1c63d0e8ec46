interface DashboardHeaderProps {
  heading: string;
  text?: string;
  children?: React.ReactNode;
}

export function DashboardHeader({
  heading,
  text,
  children,
}: DashboardHeaderProps) {
  return (
    <div className="flex flex-col items-start justify-between gap-4 border-b border-neutral-800 pb-5 pt-2 sm:flex-row sm:items-center sm:gap-0">
      <div className="flex flex-1 flex-col gap-1">
        <h1 className="text-2xl font-bold tracking-tight text-white">{heading}</h1>
        {text && <p className="text-sm text-neutral-400">{text}</p>}
      </div>
      {children}
    </div>
  );
}
