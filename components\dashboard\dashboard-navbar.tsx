"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/context/auth-context";
import { ThemeToggle } from "@/components/theme/theme-toggle";

export function DashboardNavbar() {
  const pathname = usePathname();
  const { user, signOut } = useAuth();

  const navItems = [
    {
      title: "Accueil",
      href: "/dashboard",
    },
    {
      title: "Mon Dashboard",
      href: "/dashboard/my-dashboard",
    },
    {
      title: "Mes comptes",
      href: "/dashboard/accounts",
    },
    {
      title: "Formules",
      href: "/dashboard/plans",
    },
    {
      title: "Abonnements",
      href: "/dashboard/subscriptions",
    },
    {
      title: "Ressources",
      href: "/dashboard/resources",
    },
    {
      title: "Support",
      href: "/dashboard/support",
    },
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-neutral-900 border-b border-neutral-200 dark:border-neutral-800 transition-colors duration-300">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center gap-2">
              <div className="relative h-8 w-8 rounded-full bg-white">
                <div className="absolute inset-1 rounded-full bg-purple-600"></div>
              </div>
              <span className="font-bold text-neutral-900 dark:text-white text-xl transition-colors">HexaTikPay</span>
            </Link>
            <nav className="hidden md:ml-10 md:flex md:space-x-8">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    pathname === item.href
                      ? "text-neutral-900 dark:text-white bg-neutral-200 dark:bg-neutral-800"
                      : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white hover:bg-neutral-100 dark:hover:bg-neutral-800"
                  )}
                >
                  {item.title}
                </Link>
              ))}
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <ThemeToggle className="text-neutral-600 dark:text-neutral-300" />
            <div className="hidden md:flex items-center gap-2 text-neutral-600 dark:text-neutral-300">
              <Link href="/dashboard/my-dashboard">
                <div className="h-8 w-8 rounded-full bg-neutral-200 dark:bg-neutral-800 flex items-center justify-center text-neutral-900 dark:text-white hover:bg-purple-100 dark:hover:bg-purple-800 transition-colors cursor-pointer">
                  {user?.name?.charAt(0) || 'U'}
                </div>
              </Link>
              <Link href="/dashboard/my-dashboard" className="text-sm hover:text-neutral-900 dark:hover:text-white transition-colors">
                {user?.name || 'Utilisateur'}
              </Link>
            </div>
            <Link
              href="/dashboard/profile"
              className={cn(
                "px-3 py-2 text-sm font-medium rounded-md transition-colors",
                pathname === "/dashboard/profile"
                  ? "text-neutral-900 dark:text-white bg-neutral-200 dark:bg-neutral-800"
                  : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white hover:bg-neutral-100 dark:hover:bg-neutral-800"
              )}
            >
              Mon profil
            </Link>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSignOut}
              className="border-neutral-300 dark:border-neutral-700 text-neutral-600 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
            >
              Déconnexion
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className="md:hidden border-t border-neutral-200 dark:border-neutral-800 transition-colors">
        <div className="container mx-auto px-4 py-2">
          <nav className="flex overflow-x-auto pb-1 scrollbar-hide">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "px-3 py-2 text-sm font-medium rounded-md whitespace-nowrap mr-2 transition-colors",
                  pathname === item.href
                    ? "text-neutral-900 dark:text-white bg-neutral-200 dark:bg-neutral-800"
                    : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white hover:bg-neutral-100 dark:hover:bg-neutral-800"
                )}
              >
                {item.title}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </header>
  );
}
