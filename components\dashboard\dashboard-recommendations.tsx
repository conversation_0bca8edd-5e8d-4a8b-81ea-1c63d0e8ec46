"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const recommendations = [
  {
    id: 1,
    title: "Passez à Premium",
    description: "Optimisez votre bio et obtenez 100 hashtags performants",
    cta: "Découvrir Premium",
    href: "/dashboard/plans",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-purple-400"
      >
        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
      </svg>
    ),
  },
  {
    id: 2,
    title: "Téléchargez notre guide",
    description: "10 astuces pour accroître la rétention de votre audience",
    cta: "Télécharger",
    href: "/dashboard/resources",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-blue-400"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
      </svg>
    ),
  },
  {
    id: 3,
    title: "Parrainez vos amis",
    description: "Obtenez 20% de réduction sur votre prochain achat",
    cta: "Parrainer",
    href: "/dashboard/referrals",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-green-400"
      >
        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
        <circle cx="9" cy="7" r="4" />
        <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
        <path d="M16 3.13a4 4 0 0 1 0 7.75" />
      </svg>
    ),
  },
];

export function DashboardRecommendations() {
  return (
    <Card className="col-span-1 lg:col-span-1 bg-neutral-900 border-neutral-800">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-white">Recommandations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recommendations.map((recommendation) => (
            <div
              key={recommendation.id}
              className="flex items-start gap-4 border-b border-neutral-800 pb-4 last:border-0 last:pb-0"
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-neutral-800">
                {recommendation.icon}
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-white">{recommendation.title}</h3>
                <p className="text-sm text-neutral-400 mb-2">{recommendation.description}</p>
                <Link href={recommendation.href}>
                  <Button 
                    variant="link" 
                    className="p-0 h-auto text-purple-400 hover:text-purple-300"
                  >
                    {recommendation.cta}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="ml-1 h-4 w-4"
                    >
                      <path d="M5 12h14" />
                      <path d="m12 5 7 7-7 7" />
                    </svg>
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
