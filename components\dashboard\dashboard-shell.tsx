"use client";

import { DashboardNav } from "@/components/dashboard/dashboard-nav";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface DashboardShellProps extends React.HTMLAttributes<HTMLDivElement> {}

export function DashboardShell({
  children,
  className,
  ...props
}: DashboardShellProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex flex-1">
        <DashboardNav 
          isOpen={sidebarOpen} 
          onToggle={() => setSidebarOpen(!sidebarOpen)} 
        />
        <main
          className={cn(
            "flex-1 bg-background pb-10 pt-16",
            sidebarOpen ? "md:pl-64" : "md:pl-20",
            className
          )}
          {...props}
        >
          <div className="px-4 sm:px-6 lg:px-8">{children}</div>
        </main>
      </div>
    </div>
  );
}
