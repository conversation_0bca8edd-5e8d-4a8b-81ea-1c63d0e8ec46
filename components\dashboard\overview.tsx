"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer 
} from "recharts";

const data = [
  { name: "<PERSON>", value: 2400 },
  { name: "Feb", value: 1398 },
  { name: "<PERSON>", value: 9800 },
  { name: "Apr", value: 3908 },
  { name: "May", value: 4800 },
  { name: "<PERSON>", value: 3800 },
  { name: "Jul", value: 4300 },
];

export function Overview() {
  return (
    <Card className="col-span-3 bg-neutral-900 border-neutral-800">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-white">Portfolio Overview</CardTitle>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-neutral-400">Last 7 days</span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-white">$45,231.89</div>
        <div className="flex items-center mt-1 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4 text-green-500 mr-1"
          >
            <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" />
            <polyline points="16 7 22 7 22 13" />
          </svg>
          <span className="text-xs font-medium text-green-500">+12.5%</span>
          <span className="text-xs text-neutral-400 ml-1">from last month</span>
        </div>
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{
                top: 5,
                right: 0,
                left: 0,
                bottom: 5,
              }}
            >
              <defs>
                <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#333" />
              <XAxis 
                dataKey="name" 
                tick={{ fill: '#999' }} 
                axisLine={{ stroke: '#333' }} 
              />
              <YAxis 
                tick={{ fill: '#999' }} 
                axisLine={{ stroke: '#333' }} 
              />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#222', 
                  borderColor: '#333',
                  color: '#fff' 
                }} 
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#8884d8"
                fillOpacity={1}
                fill="url(#colorValue)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
