"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

const plans = [
  {
    id: "premium",
    name: "Premium",
    price: 199,
    description: "Pour les créateurs qui veulent maximiser leur potentiel",
    features: [
      "Tout ce qu'il y a dans Pro, plus :",
      "Création personnalisée de logo (PNG + SVG)",
      "Optimisation bio professionnelle",
      "Sélection de 100 hashtags performants",
      "Guide PDF (10 pages) \"Booster vos vues & abonnés\"",
    ],
    cta: "Choisir Premium",
    popular: false,
    color: "purple",
    roi: "~500 € mensuels en 1 mois, ROI ≈ 250%",
    payment: "Paiement unique ou 2× 100 €",
  },
  {
    id: "pro",
    name: "Pro",
    price: 49,
    description: "Notre formule la plus populaire",
    features: [
      "Choix du nom d'utilisateur (conforme TikTok)",
      "Pays d'origine (France ou États-Unis)",
      "Création manuelle du compte",
      "Activation de la monétisation de base",
      "Sélection de 5 hashtags performants",
      "30 min de consulting stratégique en ligne",
    ],
    cta: "Choisir Pro",
    popular: true,
    color: "blue",
    roi: "~100 € en 2 semaines, ROI > 200%",
    payment: "Paiement unique ou 2× 25 €",
  },
  {
    id: "standard",
    name: "Standard",
    price: 10,
    description: "Pour tester sans risque",
    features: [
      "Compte pré-créé avec un nom d'utilisateur aléatoire",
      "Activation instantanée de la monétisation",
      "Sélection automatique de la catégorie de compte",
      "Livraison en moins de 2h",
    ],
    cta: "Choisir Standard",
    popular: false,
    color: "green",
    roi: "~20 € en 1 mois, ROI 200%",
    payment: "Paiement unique",
  },
];

export function PlanCards() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {plans.map((plan) => (
        <Card 
          key={plan.id} 
          className={`bg-neutral-900 border-neutral-800 ${
            plan.popular ? 'ring-2 ring-purple-500 relative' : ''
          }`}
        >
          {plan.popular && (
            <div className="absolute -top-3 left-0 right-0 flex justify-center">
              <span className="bg-purple-500 text-white text-xs font-medium px-3 py-1 rounded-full">
                Meilleur choix
              </span>
            </div>
          )}
          <CardHeader>
            <CardTitle className={`text-${plan.color}-400`}>{plan.name}</CardTitle>
            <CardDescription className="text-neutral-400">{plan.description}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <span className="text-3xl font-bold text-white">{plan.price} €</span>
              <span className="text-neutral-400 ml-1 text-sm">{plan.payment}</span>
            </div>
            <div className="text-xs text-neutral-400 bg-neutral-800 p-2 rounded">
              ROI estimé: {plan.roi}
            </div>
            <ul className="space-y-2 text-sm text-neutral-300">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className={`mr-2 h-4 w-4 text-${plan.color}-400 shrink-0 mt-0.5`}
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                  {feature}
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Link href={`/dashboard/checkout?plan=${plan.id}`} className="w-full">
              <Button 
                className={`w-full ${
                  plan.popular 
                    ? 'bg-purple-600 hover:bg-purple-700 text-white' 
                    : 'bg-neutral-800 hover:bg-neutral-700 text-white'
                }`}
              >
                {plan.cta}
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
