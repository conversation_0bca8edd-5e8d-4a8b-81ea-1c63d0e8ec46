"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const paymentMethods = [
  {
    id: 1,
    type: "card",
    brand: "Visa",
    last4: "4242",
    expMonth: 12,
    expYear: 2025,
    isDefault: true,
  },
  {
    id: 2,
    type: "card",
    brand: "Mastercard",
    last4: "5555",
    expMonth: 8,
    expYear: 2024,
    isDefault: false,
  },
];

export function PaymentMethods() {
  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-white">Moyens de paiement</CardTitle>
        <Button 
          variant="outline" 
          size="sm" 
          className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
        >
          Ajouter
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {paymentMethods.map((method) => (
            <div 
              key={method.id} 
              className={`flex items-center justify-between p-4 rounded-lg ${
                method.isDefault 
                  ? "bg-neutral-800 border border-neutral-700" 
                  : "bg-neutral-800/50"
              }`}
            >
              <div className="flex items-center gap-3">
                <div className="h-10 w-16 bg-neutral-700 rounded flex items-center justify-center text-white font-medium">
                  {method.brand}
                </div>
                <div>
                  <div className="font-medium text-white">•••• {method.last4}</div>
                  <div className="text-xs text-neutral-400">
                    Expire {method.expMonth}/{method.expYear}
                    {method.isDefault && (
                      <span className="ml-2 text-purple-400">Par défaut</span>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {!method.isDefault && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-neutral-400 hover:text-white hover:bg-neutral-700"
                  >
                    Par défaut
                  </Button>
                )}
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-neutral-400 hover:text-white hover:bg-neutral-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <path d="M12 20h9" />
                    <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z" />
                  </svg>
                  <span className="sr-only">Modifier</span>
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <path d="M3 6h18" />
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                  </svg>
                  <span className="sr-only">Supprimer</span>
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-6 border-t border-neutral-800">
          <h3 className="text-sm font-medium text-white mb-4">Historique des transactions</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-neutral-800 flex items-center justify-center text-neutral-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                  </svg>
                </div>
                <div>
                  <div className="text-white">Achat formule Premium</div>
                  <div className="text-neutral-400">15 juin 2023</div>
                </div>
              </div>
              <div className="text-white">199,00 €</div>
            </div>
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-neutral-800 flex items-center justify-center text-neutral-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                  </svg>
                </div>
                <div>
                  <div className="text-white">Abonnement Premium</div>
                  <div className="text-neutral-400">1 juin 2023</div>
                </div>
              </div>
              <div className="text-white">29,99 €</div>
            </div>
          </div>
          <div className="mt-4 text-center">
            <Button 
              variant="link" 
              className="text-purple-400 hover:text-purple-300"
            >
              Voir toutes les transactions
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
