"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";

export function SecuritySettings() {
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Simuler le changement de mot de passe
    setTimeout(() => {
      setIsChangingPassword(false);
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    }, 500);
  };
  
  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">Sécurité</CardTitle>
      </CardHeader>
      <CardContent>
        {isChangingPassword ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="currentPassword" className="text-neutral-400">Mot de passe actuel</Label>
              <Input
                id="currentPassword"
                name="currentPassword"
                type="password"
                value={passwordData.currentPassword}
                onChange={handleChange}
                required
                className="bg-neutral-800 border-neutral-700 text-white mt-1"
              />
            </div>
            <div>
              <Label htmlFor="newPassword" className="text-neutral-400">Nouveau mot de passe</Label>
              <Input
                id="newPassword"
                name="newPassword"
                type="password"
                value={passwordData.newPassword}
                onChange={handleChange}
                required
                className="bg-neutral-800 border-neutral-700 text-white mt-1"
              />
            </div>
            <div>
              <Label htmlFor="confirmPassword" className="text-neutral-400">Confirmer le mot de passe</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={passwordData.confirmPassword}
                onChange={handleChange}
                required
                className="bg-neutral-800 border-neutral-700 text-white mt-1"
              />
            </div>
            <div className="pt-2 flex gap-2">
              <Button type="submit" className="bg-purple-600 hover:bg-purple-700 text-white">
                Changer le mot de passe
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
                onClick={() => setIsChangingPassword(false)}
              >
                Annuler
              </Button>
            </div>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium text-white">Mot de passe</h3>
                <p className="text-sm text-neutral-400">Dernière modification il y a 3 mois</p>
              </div>
              <Button 
                variant="outline" 
                className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
                onClick={() => setIsChangingPassword(true)}
              >
                Modifier
              </Button>
            </div>
            
            <div className="flex justify-between items-center pt-4 border-t border-neutral-800">
              <div>
                <h3 className="font-medium text-white">Authentification à deux facteurs</h3>
                <p className="text-sm text-neutral-400">Protégez votre compte avec une sécurité supplémentaire</p>
              </div>
              <Button 
                variant="outline" 
                className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
              >
                Activer
              </Button>
            </div>
            
            <div className="flex justify-between items-center pt-4 border-t border-neutral-800">
              <div>
                <h3 className="font-medium text-white">Sessions actives</h3>
                <p className="text-sm text-neutral-400">Gérez vos sessions connectées</p>
              </div>
              <Button 
                variant="outline" 
                className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
              >
                Gérer
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
