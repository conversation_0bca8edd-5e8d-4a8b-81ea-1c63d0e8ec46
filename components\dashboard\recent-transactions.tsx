"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

const transactions = [
  {
    id: 1,
    type: "Buy",
    asset: "Bitcoin",
    symbol: "BTC",
    amount: 0.05,
    value: 2145.67,
    date: "2023-06-15T10:30:00Z",
    status: "Completed",
  },
  {
    id: 2,
    type: "Sell",
    asset: "Ethereum",
    symbol: "ETH",
    amount: 1.2,
    value: 2789.45,
    date: "2023-06-14T14:45:00Z",
    status: "Completed",
  },
  {
    id: 3,
    type: "Buy",
    asset: "Solana",
    symbol: "SOL",
    amount: 15.6,
    value: 1532.12,
    date: "2023-06-12T09:15:00Z",
    status: "Completed",
  },
  {
    id: 4,
    type: "Transfer",
    asset: "Cardano",
    symbol: "ADA",
    amount: 450,
    value: 675.34,
    date: "2023-06-10T16:20:00Z",
    status: "Pending",
  },
];

export function RecentTransactions() {
  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-white">Recent Transactions</CardTitle>
        <button className="text-xs text-purple-400 hover:text-purple-300">
          View All
        </button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between border-b border-neutral-800 pb-4 last:border-0 last:pb-0"
            >
              <div className="flex items-center gap-3">
                <div
                  className={`flex h-9 w-9 items-center justify-center rounded-full ${
                    transaction.type === "Buy"
                      ? "bg-green-500/20 text-green-500"
                      : transaction.type === "Sell"
                      ? "bg-red-500/20 text-red-500"
                      : "bg-blue-500/20 text-blue-500"
                  }`}
                >
                  {transaction.type === "Buy" ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m5 12 7-7 7 7" />
                      <path d="M12 19V5" />
                    </svg>
                  ) : transaction.type === "Sell" ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m19 12-7 7-7-7" />
                      <path d="M12 5v14" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="M17 3v10" />
                      <path d="m21 7-4-4-4 4" />
                      <path d="M7 21v-10" />
                      <path d="m3 17 4 4 4-4" />
                    </svg>
                  )}
                </div>
                <div>
                  <div className="font-medium text-white">
                    {transaction.type} {transaction.asset}
                  </div>
                  <div className="text-xs text-neutral-400">
                    {new Date(transaction.date).toLocaleDateString()}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-white">
                  ${transaction.value.toLocaleString()}
                </div>
                <div className="flex items-center justify-end gap-1">
                  <span
                    className={`text-xs ${
                      transaction.status === "Completed"
                        ? "text-green-500"
                        : "text-yellow-500"
                    }`}
                  >
                    {transaction.status}
                  </span>
                  <span className="text-xs text-neutral-400">
                    {transaction.amount} {transaction.symbol}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
