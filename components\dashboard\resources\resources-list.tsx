"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

const resources = [
  {
    id: 1,
    title: "Guide complet TikTok 2024",
    description: "Tout ce que vous devez savoir pour réussir sur TikTok en 2024",
    type: "PDF",
    size: "4.2 MB",
    premium: true,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-red-400"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
      </svg>
    ),
  },
  {
    id: 2,
    title: "10 astuces pour augmenter vos vues",
    description: "Techniques éprouvées pour augmenter votre visibilité",
    type: "PDF",
    size: "2.1 MB",
    premium: false,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-red-400"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
      </svg>
    ),
  },
  {
    id: 3,
    title: "Tutoriel : Optimiser votre bio TikTok",
    description: "Comment créer une bio qui convertit vos visiteurs en abonnés",
    type: "Vidéo",
    size: "15 min",
    premium: true,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-blue-400"
      >
        <path d="m22 8-6 4 6 4V8Z" />
        <rect width="14" height="12" x="2" y="6" rx="2" ry="2" />
      </svg>
    ),
  },
  {
    id: 4,
    title: "Liste de 100 hashtags performants",
    description: "Les hashtags les plus efficaces par catégorie",
    type: "Excel",
    size: "1.5 MB",
    premium: true,
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-green-400"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
      </svg>
    ),
  },
];

export function ResourcesList() {
  return (
    <div>
      <h2 className="text-xl font-semibold text-white mb-4">Guides et tutoriels</h2>
      <div className="grid gap-4 md:grid-cols-2">
        {resources.map((resource) => (
          <Card key={resource.id} className="bg-neutral-900 border-neutral-800">
            <CardContent className="p-6">
              <div className="flex gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-neutral-800 flex-shrink-0">
                  {resource.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-white">{resource.title}</h3>
                    {resource.premium && (
                      <div className="text-xs px-2 py-0.5 rounded-full bg-purple-500/20 text-purple-400">
                        Premium
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-neutral-400 mt-1 mb-3">{resource.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-neutral-500">
                      {resource.type} • {resource.size}
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
                    >
                      Télécharger
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
