"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

const webinars = [
  {
    id: 1,
    title: "Comment atteindre 100K abonnés en 3 mois",
    date: "2023-08-15T18:00:00Z",
    duration: "60 min",
    host: "<PERSON>",
    status: "replay",
    premium: true,
  },
  {
    id: 2,
    title: "Monétiser efficacement votre compte TikTok",
    date: "2023-09-05T19:00:00Z",
    duration: "45 min",
    host: "<PERSON>",
    status: "replay",
    premium: false,
  },
  {
    id: 3,
    title: "Les tendances TikTok à suivre en 2024",
    date: "2024-01-20T20:00:00Z",
    duration: "60 min",
    host: "<PERSON>",
    status: "upcoming",
    premium: true,
  },
];

export function WebinarsList() {
  // Formater les dates
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    }) + ' à ' + date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  return (
    <div>
      <h2 className="text-xl font-semibold text-white mb-4">Webinars</h2>
      <div className="space-y-4">
        {webinars.map((webinar) => (
          <Card key={webinar.id} className="bg-neutral-900 border-neutral-800">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row md:items-center gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium text-white">{webinar.title}</h3>
                    {webinar.premium && (
                      <div className="text-xs px-2 py-0.5 rounded-full bg-purple-500/20 text-purple-400">
                        Premium
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-sm text-neutral-400 mt-1">
                    <div className="flex items-center gap-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4"
                      >
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="12 6 12 12 16 14" />
                      </svg>
                      <span>{formatDate(webinar.date)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4"
                      >
                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                        <circle cx="12" cy="7" r="4" />
                      </svg>
                      <span>{webinar.host}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4"
                      >
                        <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" />
                      </svg>
                      <span>{webinar.duration}</span>
                    </div>
                  </div>
                </div>
                <div>
                  {webinar.status === "upcoming" ? (
                    <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                      S'inscrire
                    </Button>
                  ) : (
                    <Button 
                      variant="outline" 
                      className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800"
                    >
                      Voir le replay
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
