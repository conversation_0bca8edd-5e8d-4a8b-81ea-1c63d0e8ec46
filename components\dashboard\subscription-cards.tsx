"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";

const subscriptions = [
  {
    id: "enterprise",
    name: "Compte Entreprise",
    price: 1500,
    period: "an",
    description: "Partenariat exclusif et accès à l'API (sur invitation uniquement)",
    features: [
      "Compte en reprise (déjà existant, multi-utilisation)",
      "Accès anticipé à l'API interne (en développement)",
      "Génération automatique de comptes dérivés",
      "Obligation de publier régulièrement sur TikTok",
      "Co-branding pour une visibilité mutuelle",
      "Support prioritaire 24/7",
    ],
    cta: "Demander une invitation",
    exclusive: true,
    color: "yellow",
    roi: "> 2 000 €/mois pour certains partenaires, ROI annuel > 100%",
  },
  {
    id: "premium-sub",
    name: "Abonnement Premium",
    price: 29.99,
    period: "mois",
    description: "20% de réduction sur toutes les formules",
    features: [
      "20% de réduction sur les formules Standard, Pro et Premium",
      "Accès prioritaire aux nouveaux comptes",
      "Support dédié par email",
      "Webinars exclusifs mensuels",
      "Analyses de performance avancées",
    ],
    cta: "S'abonner",
    exclusive: false,
    color: "purple",
    roi: "Économisez jusqu'à 40€ par compte Premium",
  },
  {
    id: "basic-sub",
    name: "Abonnement Standard",
    price: 9.99,
    period: "mois",
    description: "10% de réduction sur toutes les formules",
    features: [
      "10% de réduction sur les formules Standard, Pro et Premium",
      "Support par email sous 24h",
      "Accès aux guides et ressources exclusives",
      "Notifications des meilleures opportunités",
    ],
    cta: "S'abonner",
    exclusive: false,
    color: "blue",
    roi: "Économisez jusqu'à 20€ par compte Premium",
  },
];

export function SubscriptionCards() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {subscriptions.map((subscription) => (
        <Card 
          key={subscription.id} 
          className={`bg-neutral-900 border-neutral-800 ${
            subscription.exclusive ? 'ring-2 ring-yellow-500 relative' : ''
          }`}
        >
          {subscription.exclusive && (
            <div className="absolute -top-3 left-0 right-0 flex justify-center">
              <span className="bg-yellow-500 text-black text-xs font-medium px-3 py-1 rounded-full">
                Sur invitation uniquement
              </span>
            </div>
          )}
          <CardHeader>
            <CardTitle className={`text-${subscription.color}-400`}>{subscription.name}</CardTitle>
            <CardDescription className="text-neutral-400">{subscription.description}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <span className="text-3xl font-bold text-white">{subscription.price} €</span>
              <span className="text-neutral-400 ml-1 text-sm">/{subscription.period}</span>
            </div>
            {subscription.roi && (
              <div className="text-xs text-neutral-400 bg-neutral-800 p-2 rounded">
                {subscription.roi}
              </div>
            )}
            <ul className="space-y-2 text-sm text-neutral-300">
              {subscription.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className={`mr-2 h-4 w-4 text-${subscription.color}-400 shrink-0 mt-0.5`}
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                  {feature}
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Link href={`/dashboard/checkout?subscription=${subscription.id}`} className="w-full">
              <Button 
                className={`w-full ${
                  subscription.exclusive 
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                    : 'bg-neutral-800 hover:bg-neutral-700 text-white'
                }`}
              >
                {subscription.cta}
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
