"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { mockSubscription } from "@/lib/mock/auth-mock";

export function CurrentSubscription() {
  // Formater les dates
  const startDate = new Date(mockSubscription.startDate).toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
  
  const nextBillingDate = new Date(mockSubscription.nextBillingDate).toLocaleDateString('fr-FR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
  
  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">Votre abonnement actuel</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-xl font-bold text-white">Abonnement {mockSubscription.type}</h3>
              <div className={`text-xs px-2 py-0.5 rounded-full ${
                mockSubscription.status === "Actif" 
                  ? "bg-green-500/20 text-green-400" 
                  : "bg-yellow-500/20 text-yellow-400"
              }`}>
                {mockSubscription.status}
              </div>
            </div>
            <p className="text-neutral-400 text-sm mt-1">
              {mockSubscription.discount}% de réduction sur toutes les formules
            </p>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold text-white">{mockSubscription.price} €<span className="text-sm text-neutral-400">/mois</span></div>
            <p className="text-neutral-400 text-sm">Prochain prélèvement le {nextBillingDate}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-neutral-800">
          <div>
            <h4 className="text-sm font-medium text-neutral-400 mb-1">Date de début</h4>
            <p className="text-white">{startDate}</p>
          </div>
          <div>
            <h4 className="text-sm font-medium text-neutral-400 mb-1">Méthode de paiement</h4>
            <div className="flex items-center gap-2">
              <div className="h-6 w-10 bg-neutral-800 rounded flex items-center justify-center text-xs text-white">
                VISA
              </div>
              <span className="text-white">•••• 4242</span>
            </div>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2 pt-4">
          <Button variant="outline" className="border-neutral-700 text-neutral-300 hover:text-white hover:bg-neutral-800">
            Modifier le moyen de paiement
          </Button>
          <Button variant="outline" className="border-red-900/50 text-red-400 hover:text-red-300 hover:bg-red-900/20">
            Annuler l'abonnement
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
