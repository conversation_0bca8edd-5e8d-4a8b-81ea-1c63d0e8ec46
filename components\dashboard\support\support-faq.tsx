"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

const faqItems = [
  {
    id: 1,
    question: "Comment fonctionne la monétisation sur TikTok ?",
    answer: "La monétisation sur TikTok fonctionne principalement via le Fonds des créateurs TikTok, les partenariats avec les marques, les pourboires en direct, et les ventes de produits. Pour être éligible au Fonds des créateurs, vous devez avoir au moins 10 000 abonnés et 100 000 vues sur vos vidéos au cours des 30 derniers jours.",
  },
  {
    id: 2,
    question: "Combien de temps faut-il pour recevoir mon compte TikTok ?",
    answer: "Le délai de livraison dépend de la formule choisie. Pour la formule Standard, la livraison est généralement effectuée en moins de 2 heures. Pour les formules Pro et Premium, le délai peut aller de 24 à 48 heures en raison de la personnalisation et de l'optimisation manuelle du compte.",
  },
  {
    id: 3,
    question: "Puis-je changer de formule après avoir acheté un compte ?",
    answer: "Oui, vous pouvez effectuer une mise à niveau de votre compte à tout moment. Si vous avez un compte Standard, vous pouvez passer à Pro ou Premium. Si vous avez un compte Pro, vous pouvez passer à Premium. Contactez notre support pour effectuer cette mise à niveau.",
  },
  {
    id: 4,
    question: "Les comptes sont-ils conformes aux conditions d'utilisation de TikTok ?",
    answer: "Oui, tous nos comptes sont créés dans le respect des conditions d'utilisation de TikTok. Nous utilisons des méthodes légitimes pour créer et monétiser les comptes, ce qui garantit leur pérennité et leur conformité avec les règles de la plateforme.",
  },
  {
    id: 5,
    question: "Que se passe-t-il si mon compte est suspendu ?",
    answer: "Si votre compte est suspendu dans les 30 jours suivant l'achat et que vous avez respecté nos recommandations d'utilisation, nous vous fournirons un compte de remplacement gratuitement. Pour les formules Premium et les abonnements, cette garantie est étendue à 90 jours.",
  },
];

export function SupportFaq() {
  const [openItem, setOpenItem] = useState<number | null>(null);
  
  const toggleItem = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };
  
  return (
    <Card className="bg-neutral-900 border-neutral-800 mt-6">
      <CardHeader>
        <CardTitle className="text-white">Questions fréquentes</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {faqItems.map((item) => (
          <div 
            key={item.id} 
            className={`border-b border-neutral-800 pb-4 last:border-0 last:pb-0 ${
              openItem === item.id ? "" : ""
            }`}
          >
            <button
              className="flex w-full items-center justify-between text-left"
              onClick={() => toggleItem(item.id)}
            >
              <h3 className="font-medium text-white">{item.question}</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`h-4 w-4 text-neutral-400 transition-transform ${
                  openItem === item.id ? "rotate-180" : ""
                }`}
              >
                <polyline points="6 9 12 15 18 9" />
              </svg>
            </button>
            {openItem === item.id && (
              <div className="mt-2 text-sm text-neutral-400">
                {item.answer}
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
