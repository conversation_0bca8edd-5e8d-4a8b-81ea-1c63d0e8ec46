"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

const supportOptions = [
  {
    id: 1,
    title: "Chat en direct",
    description: "Discutez en temps réel avec notre équipe de support",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-purple-400"
      >
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
      </svg>
    ),
    action: "Démarrer un chat",
    premium: true,
    href: "#chat",
  },
  {
    id: 2,
    title: "Email",
    description: "Envoyez-nous un email, nous vous répondrons sous 24h",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-blue-400"
      >
        <rect width="20" height="16" x="2" y="4" rx="2" />
        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
      </svg>
    ),
    action: "Envoyer un email",
    premium: false,
    href: "mailto:<EMAIL>",
  },
  {
    id: 3,
    title: "Centre d'aide",
    description: "Consultez notre base de connaissances",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-green-400"
      >
        <circle cx="12" cy="12" r="10" />
        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
        <path d="M12 17h.01" />
      </svg>
    ),
    action: "Consulter",
    premium: false,
    href: "#help-center",
  },
  {
    id: 4,
    title: "Consultation privée",
    description: "Réservez un appel avec un expert TikTok",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-yellow-400"
      >
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
      </svg>
    ),
    action: "Réserver",
    premium: true,
    href: "#consultation",
  },
];

export function SupportOptions() {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      {supportOptions.map((option) => (
        <Card key={option.id} className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-6">
            <div className="flex gap-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-neutral-800 flex-shrink-0">
                {option.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-white">{option.title}</h3>
                  {option.premium && (
                    <div className="text-xs px-2 py-0.5 rounded-full bg-purple-500/20 text-purple-400">
                      Premium
                    </div>
                  )}
                </div>
                <p className="text-sm text-neutral-400 mt-1 mb-3">{option.description}</p>
                <Link href={option.href}>
                  <Button 
                    className="bg-neutral-800 hover:bg-neutral-700 text-white"
                  >
                    {option.action}
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
