"use client";

import { Card, CardContent } from "@/components/ui/card";

const features = [
  {
    title: "Comptes TikTok Monétisés",
    description: "Accédez à des comptes TikTok déjà monétis<PERSON>, prêts à générer des revenus dès le premier jour.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-purple-400"
      >
        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
      </svg>
    ),
  },
  {
    title: "Optimisation Professionnelle",
    description: "Nos experts optimisent votre profil avec des hashtags performants et une bio attractive.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-blue-400"
      >
        <path d="M12 20h9" />
        <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z" />
      </svg>
    ),
  },
  {
    title: "Ressources Exclusives",
    description: "Accédez à des guides, tutoriels et webinars pour maximiser vos performances sur TikTok.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-green-400"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
      </svg>
    ),
  },
  {
    title: "Support Dédié",
    description: "Une équipe de support disponible pour vous accompagner à chaque étape de votre parcours.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="h-6 w-6 text-yellow-400"
      >
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
      </svg>
    ),
  },
];

export function FeatureHighlights() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {features.map((feature, index) => (
        <Card key={index} className="bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800 transition-colors duration-300">
          <CardContent className="p-6">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-neutral-100 dark:bg-neutral-800 mb-4 transition-colors duration-300">
              {feature.icon}
            </div>
            <h3 className="text-lg font-medium text-neutral-900 dark:text-white mb-2 transition-colors duration-300">{feature.title}</h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 transition-colors duration-300">{feature.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
