"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const promoSpots = [
  {
    title: "Formule Premium",
    description: "Tout ce dont vous avez besoin pour réussir sur TikTok. Optimisation bio professionnelle, 100 hashtags performants et bien plus.",
    cta: "Découvrir Premium",
    href: "/dashboard/plans",
    color: "purple",
    image: "bg-gradient-to-br from-purple-600 to-purple-900",
  },
  {
    title: "Abonnement mensuel",
    description: "Économisez jusqu'à 20% sur toutes vos formules avec notre abonnement Premium. Accès prioritaire aux nouveaux comptes.",
    cta: "Voir les abonnements",
    href: "/dashboard/subscriptions",
    color: "blue",
    image: "bg-gradient-to-br from-blue-600 to-blue-900",
  },
];

export function PromoSpots() {
  return (
    <div className="grid gap-6 md:grid-cols-2">
      {promoSpots.map((spot, index) => (
        <Card key={index} className="bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800 overflow-hidden transition-colors duration-300">
          <CardContent className="p-0">
            <div className="flex flex-col md:flex-row">
              <div className={`${spot.image} p-6 md:w-1/3 flex items-center justify-center`}>
                <div className="h-20 w-20 rounded-full bg-white/20 flex items-center justify-center transition-colors duration-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-10 w-10 text-white transition-colors duration-300"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </div>
              </div>
              <div className="p-6 md:w-2/3">
                <h3 className={`text-xl font-medium text-${spot.color}-600 dark:text-${spot.color}-400 mb-2 transition-colors duration-300`}>{spot.title}</h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-4 transition-colors duration-300">{spot.description}</p>
                <Link href={spot.href}>
                  <Button
                    className={`bg-${spot.color}-600 hover:bg-${spot.color}-700 text-white transition-colors duration-300`}
                  >
                    {spot.cta}
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
