"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { mockAccounts, mockStats } from "@/lib/mock/auth-mock";

export function QuickStats() {
  // Calculer le nombre de comptes actifs
  const activeAccounts = mockAccounts.filter(account => account.status === "Actif").length;

  return (
    <Card className="bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800 transition-colors duration-300">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h3 className="text-lg font-medium text-neutral-900 dark:text-white mb-1 transition-colors duration-300">Aperçu rapide</h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4 md:mb-0 transition-colors duration-300">
              Voici un résumé de vos comptes et performances
            </p>
          </div>
          <div className="flex flex-wrap gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-neutral-900 dark:text-white transition-colors duration-300">{mockAccounts.length}</div>
              <div className="text-xs text-neutral-600 dark:text-neutral-400 transition-colors duration-300">Comptes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-neutral-900 dark:text-white transition-colors duration-300">{activeAccounts}</div>
              <div className="text-xs text-neutral-600 dark:text-neutral-400 transition-colors duration-300">Actifs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-neutral-900 dark:text-white transition-colors duration-300">{mockStats.totalRevenue}€</div>
              <div className="text-xs text-neutral-600 dark:text-neutral-400 transition-colors duration-300">Revenus</div>
            </div>
          </div>
          <Link href="/dashboard/accounts">
            <Button variant="outline" className="border-neutral-300 dark:border-neutral-700 text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 hover:bg-neutral-100 dark:hover:text-white dark:hover:bg-neutral-800 transition-colors duration-300">
              Voir détails
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
