"use client";

import { Card, CardContent } from "@/components/ui/card";

const testimonials = [
  {
    quote: "<PERSON>r<PERSON><PERSON> à HexaTikPay, j'ai pu générer plus de 500€ par mois avec mon compte TikTok. Le support est incroyable !",
    author: "<PERSON>",
    role: "Créatrice de contenu lifestyle",
    avatar: "M",
  },
  {
    quote: "J'ai essayé plusieurs services, mais HexaTikPay est de loin le plus professionnel. Mon compte a été livré en moins de 24h.",
    author: "<PERSON>",
    role: "Influenceur gaming",
    avatar: "T",
  },
  {
    quote: "L'optimisation de ma bio a fait exploser mon nombre d'abonnés. Je recommande la formule Premium sans hésiter.",
    author: "<PERSON>",
    role: "Créatrice mode",
    avatar: "S",
  },
];

export function Testimonials() {
  return (
    <div>
      <h2 className="text-2xl font-bold text-white mb-6">Ce que disent nos clients</h2>
      <div className="grid gap-6 md:grid-cols-3">
        {testimonials.map((testimonial, index) => (
          <Card key={index} className="bg-neutral-900 border-neutral-800">
            <CardContent className="p-6">
              <div className="flex flex-col h-full">
                <div className="mb-4">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="h-5 w-5 inline-block text-yellow-400"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ))}
                </div>
                <blockquote className="text-neutral-300 flex-grow mb-4">
                  "{testimonial.quote}"
                </blockquote>
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-purple-600 flex items-center justify-center text-white font-medium mr-3">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-medium text-white">{testimonial.author}</div>
                    <div className="text-sm text-neutral-400">{testimonial.role}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
