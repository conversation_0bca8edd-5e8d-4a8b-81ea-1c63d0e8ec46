"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/context/auth-context";

export function WelcomeHero() {
  const { user } = useAuth();

  return (
    <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-600 to-purple-900 dark:from-purple-900 dark:to-black p-8 md:p-10 transition-colors duration-300">
      <div className="absolute inset-0 bg-grid-white/10 dark:bg-grid-white/5 [mask-image:linear-gradient(0deg,#fff,rgba(255,255,255,0.6))] transition-opacity duration-300" />

      <div className="relative max-w-3xl">
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-4 transition-colors duration-300">
          Bienvenue sur HexaTikPay, {user?.name?.split(' ')[0] || 'Créateur'}
        </h1>
        <p className="text-lg text-white/90 dark:text-white/80 mb-6 transition-colors duration-300">
          Votre plateforme pour acquérir, gérer et optimiser des comptes TikTok monétisés.
          Commencez dès aujourd'hui et transformez votre présence sur TikTok en source de revenus.
        </p>
        <div className="flex flex-wrap gap-4">
          <Link href="/dashboard/plans">
            <Button className="bg-white dark:bg-white text-purple-900 dark:text-purple-900 hover:bg-purple-50 dark:hover:bg-white/90 transition-colors duration-300">
              Découvrir nos formules
            </Button>
          </Link>
          <Link href="/dashboard/accounts">
            <Button variant="outline" className="border-purple-200/50 dark:border-white/30 text-white hover:bg-purple-100/10 dark:hover:bg-white/10 transition-colors duration-300">
              Voir mes comptes
            </Button>
          </Link>
        </div>
      </div>

      <div className="absolute bottom-0 right-0 -mb-12 -mr-12 h-64 w-64 rounded-full bg-purple-500/20 dark:bg-purple-600/30 blur-3xl transition-colors duration-300" />
      <div className="absolute top-1/2 right-1/4 -mt-32 -mr-32 h-48 w-48 rounded-full bg-purple-300/20 dark:bg-purple-400/20 blur-3xl transition-colors duration-300" />
    </div>
  );
}
