'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { signInAction } from "@/lib/actions/auth"

export function DevAuth() {
  const [isLoading, setIsLoading] = useState(false)
  
  const handleDevLogin = async () => {
    setIsLoading(true)
    
    try {
      // Utiliser un email et mot de passe prédéfinis pour le développement
      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password123')
      
      await signInAction(formData)
    } catch (error) {
      console.error('Dev login error:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Button 
        onClick={handleDevLogin}
        disabled={isLoading}
        className="bg-yellow-600 hover:bg-yellow-700 text-white"
      >
        {isLoading ? 'Connexion...' : 'Dev Login'}
      </Button>
    </div>
  )
}
