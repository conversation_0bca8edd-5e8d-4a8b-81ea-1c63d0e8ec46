'use client'

import { useState } from 'react'
import { 
  Drawer, 
  DrawerContent, 
  DrawerTrigger 
} from "@/components/ui/drawer"
import { Button } from "@/components/ui/button"
import AuthDrawerContent from "@/components/auth/AuthDrawerContent"

interface AuthDrawerProps {
  mode?: 'login' | 'register'
  triggerClassName?: string
  buttonVariant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  buttonText?: string
}

export function AuthDrawer({ 
  mode = 'login', 
  triggerClassName = '', 
  buttonVariant = 'default',
  buttonText
}: AuthDrawerProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleClose = () => {
    setIsOpen(false)
  }

  const defaultButtonText = mode === 'login' ? 'Login' : 'Sign Up'

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild>
        <Button 
          variant={buttonVariant} 
          className={triggerClassName}
        >
          {buttonText || defaultButtonText}
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <AuthDrawerContent initialMode={mode} onClose={handleClose} />
      </DrawerContent>
    </Drawer>
  )
}

export default AuthDrawer
