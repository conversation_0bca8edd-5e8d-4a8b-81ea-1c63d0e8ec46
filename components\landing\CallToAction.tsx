import { AuthDrawer } from "./AuthDrawer";

export function CallToAction() {
  return (
    <section className="py-16 bg-gradient-to-r from-purple-900/80 to-black text-white">
      <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to start your crypto journey?</h2>
        <p className="text-lg text-white/80 max-w-2xl mx-auto mb-8">
          Join thousands of traders who are already using HexaTikPay to maximize their investments with AI-powered insights.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <AuthDrawer 
            mode="register" 
            buttonText="Create Free Account" 
            triggerClassName="bg-white text-black hover:bg-white/90 font-semibold py-3 px-8 rounded-lg"
          />
          <AuthDrawer 
            mode="login" 
            buttonVariant="outline"
            buttonText="Sign In" 
            triggerClassName="border-white/20 text-white hover:bg-white/10 font-semibold py-3 px-8 rounded-lg"
          />
        </div>
      </div>
    </section>
  );
}
