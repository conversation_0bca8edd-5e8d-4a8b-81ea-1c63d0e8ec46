import { Button } from "@/components/ui/button";
import { AuthDrawer } from "./AuthDrawer";

export function Features() {
  return (
    <section className="py-20 bg-black text-white">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div className="bg-zinc-900/50 rounded-xl p-8 border border-white/10">
            <div className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-zinc-800 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 3a1 1 0 000 2h10a1 1 0 100-2H3zm0 4a1 1 0 000 2h10a1 1 0 100-2H3zm0 4a1 1 0 100 2h10a1 1 0 100-2H3z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Advanced tracking system. Instantly locate all your assets.</h3>
            <p className="text-white/70 mb-6">
              Our advanced tracking system provides real-time updates on all your crypto assets, allowing you to monitor performance and make informed decisions.
            </p>
            <AuthDrawer
              mode="register"
              buttonVariant="outline"
              buttonText="Get Started"
              triggerClassName="border-white/20 text-white hover:bg-white/10"
            />
          </div>

          <div className="bg-zinc-900/50 rounded-xl p-8 border border-white/10">
            <div className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-zinc-800 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Reach out via email or web for any assistance you need.</h3>
            <p className="text-white/70 mb-6">
              Our dedicated support team is available to assist you with any questions or issues you may encounter along your crypto journey.
            </p>
            <div className="flex gap-4">
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                Email Us
              </Button>
              <AuthDrawer
                mode="login"
                buttonText="Chat with support"
                triggerClassName="bg-blue-600 hover:bg-blue-700"
              />
            </div>
          </div>
        </div>

        <div className="mt-12 flex justify-center">
          <div className="bg-zinc-900/50 rounded-xl p-4 inline-flex items-center gap-2 border border-white/10">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span className="text-sm text-white/80">Get information about the product</span>
          </div>
        </div>
      </div>
    </section>
  );
}
