import Link from "next/link";
import { AuthDrawer } from "./AuthDrawer";

export function Footer() {
  return (
    <footer className="bg-black text-white pt-16 pb-8">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-12">
          <div className="col-span-2">
            <Link href="/" className="flex items-center gap-2 mb-4">
              <div className="relative h-8 w-8 rounded-full bg-white">
                <div className="absolute inset-1 rounded-full bg-primary"></div>
              </div>
              <span className="font-bold text-white text-xl">HexaTikPay</span>
            </Link>
            <p className="text-white/70 text-sm mb-4 max-w-xs">
              Welcome to HexaTikPay, where crypto trading meets AI intelligence. Our platform is designed to bring your trades to life, with personalized advice based on historical data and current market conditions.
            </p>
            <div className="flex gap-4">
              <a href="#" className="text-white/70 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
              <a href="#" className="text-white/70 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                </svg>
              </a>
              <a href="#" className="text-white/70 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h4 className="font-bold mb-4">About</h4>
            <ul className="space-y-2">
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">About Us</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Features</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Newsroom</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Careers</Link></li>
            </ul>
          </div>

          <div>
            <h4 className="font-bold mb-4">Products</h4>
            <ul className="space-y-2">
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">API</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Dashboard</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Mobile App</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Web App</Link></li>
            </ul>
          </div>

          <div>
            <h4 className="font-bold mb-4">Contact</h4>
            <ul className="space-y-2">
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Support</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Sales</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Report Abuse</Link></li>
              <li><Link href="#" className="text-white/70 hover:text-white text-sm">Privacy</Link></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-white/10 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white/50 text-sm mb-4 md:mb-0">© 2024 HexaTikPay. All rights reserved.</p>
          <div className="flex items-center gap-6">
            <Link href="#" className="text-white/50 hover:text-white text-sm">Terms</Link>
            <Link href="#" className="text-white/50 hover:text-white text-sm">Privacy</Link>
            <Link href="#" className="text-white/50 hover:text-white text-sm">Cookies</Link>
            <AuthDrawer
              mode="register"
              buttonVariant="outline"
              buttonText="Join Now"
              triggerClassName="border-white/20 text-white hover:bg-white/10 text-sm py-1 px-3 h-auto"
            />
          </div>
        </div>
      </div>
    </footer>
  );
}
