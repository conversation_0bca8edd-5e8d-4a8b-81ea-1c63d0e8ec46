"use client";

import { <PERSON>Pill } from "../ui/hero-pill";
import { motion } from "framer-motion";

function SectionLeft() {
  return (
    <div className="relative w-full">
      <div className=" flex justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h1 className="mx-auto mb-5 max-w-4xl text-4xl font-light md:text-5xl lg:text-7xl">
            Creation de comptes{" "}
            <span className="text-[#9b87f5]">TikTok</span>
          </h1>
          <p className="mx-auto mb-10 max-w-2xl text-lg text-white/60 md:text-xl">
            HexaTikPay vous permet d'acquérir des comptes TikTok déjà monétisés
            et optimisés pour maximiser vos revenus rapidement et facilement.
          </p>


        </motion.div>
      </div>
    </div>
  );
}

function SectionRight() {
  return (
    <div className="relative items-center justify-center flex w-full">
      <div className="bg-black/40 backdrop-blur-sm p-6 rounded-xl border border-white/10 max-w-md mx-auto">
        <div className="flex items-center gap-2 mb-4">
          <div className="h-3 w-3 rounded-full bg-orange-500"></div>
          <span className="text-orange-500 font-medium">Popular</span>
        </div>
        <p className="text-sm text-white/70 mb-6">
          The most popular trading asset on the market through April 2023 to
          2024.
        </p>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-white/70">Users</span>
            <span className="font-bold">8,591</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-white/70">Transactions</span>
            <span className="font-bold">5,412</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export function Hero() {
  return (
    <section className="relative flex flex-col justify-center items-center  overflow-hidden">
      <HeroPill  icon='Hexa' 
  text='Join Our Comunity ✨🫧'/>
       
      <div className="max-w-7xl mx-auto px-6 flex md:px-12 relative z-10">
        <SectionLeft />
        <SectionRight />
      </div>
    </section>
  );
}
