export function Support() {
  return (
    <section className="py-16 bg-black text-white">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-zinc-900/50 rounded-xl p-6 border border-white/10">
            <div className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-zinc-800 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
            </div>
            <h3 className="text-lg font-bold mb-2">24/7 Customer Support</h3>
            <p className="text-white/70 text-sm mb-4">
              We are available 24 hours of the day, 7 days of the week to help with any issues.
            </p>
            <div className="text-sm text-white/50">
              Average response time: 15 minutes
            </div>
          </div>
          
          <div className="bg-zinc-900/50 rounded-xl p-6 border border-white/10">
            <div className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-zinc-800 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-bold mb-2">Money back guarantee</h3>
            <p className="text-white/70 text-sm mb-4">
              If you're not 100% satisfied, we will refund your payment. No questions asked.
            </p>
            <div className="text-sm text-white/50">
              30-day money-back guarantee
            </div>
          </div>
          
          <div className="bg-zinc-900/50 rounded-xl p-6 border border-white/10">
            <div className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-zinc-800 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z" />
              </svg>
            </div>
            <h3 className="text-lg font-bold mb-2">And everything else</h3>
            <p className="text-white/70 text-sm mb-4">
              Detailed API docs, SDKs, integration guides, and comprehensive documentation.
            </p>
            <div className="text-sm text-white/50">
              Updated regularly
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
