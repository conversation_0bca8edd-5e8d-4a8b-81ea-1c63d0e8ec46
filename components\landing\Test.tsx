// Background component with gradient and noise effects
import Background from "./Background";
import { Navbar } from "./Navbar";
import { <PERSON> } from "./Hero";
import Slider from "./Slider";
import { Features } from "./Features";
import AuthDrawer from "./AuthDrawer";
export default function Test() {
  return (
    <div className="flex justify-center flex-wrap relative min-h-screen overflow-hidden bg-black">
      {/* Gradient background with grain effect */}
      <Background />
      <div className="absolute inset-0 z-0 bg-noise opacity-30"></div>

      <header className="mt-6 flex w-full flex-col justify-center">
        <Navbar />
      </header>
      <section id="welcome" className="mt-6  flex w-full flex-col items-center justify-center">
        <div className="flex pt-32 pb-10 flex-col justify-center">
          <Hero />
          <div className="mb-10 sm:mb-0 flex flex-col items-center justify-center gap-4 sm:flex-row">
            <AuthDrawer
              mode="register"
              buttonText="Commencer"
              triggerClassName="neumorphic-button hover:shadow-[0_0_20px_rgba(155, 135, 245, 0.5)] relative w-full overflow-hidden rounded-full border border-white/10 bg-gradient-to-b from-white/10 to-white/5 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:border-[#9b87f5]/30 sm:w-auto"
            />
            <a
              href="#how-it-works"
              className="flex w-full items-center justify-center gap-2 text-white/70 transition-colors hover:text-white sm:w-auto"
            >
              <span>Comment ça marche</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m6 9 6 6 6-6"></path>
              </svg>
            </a>
          </div>
        </div>
        <Slider />
      </section>
      <section id="features">
        <Features />
      </section>
      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl font-bold text-white mb-6">
          Welcome to HexaTikPay
        </h1>
        <p className="text-xl text-white/80 max-w-2xl mx-auto mb-8">
          Your platform for acquiring, managing, and optimizing monetized TikTok
          accounts. Start today and transform your TikTok presence into a
          revenue source.
        </p>
        <button className="bg-white text-black px-6 py-3 rounded-md font-medium hover:bg-white/90 transition-colors">
          Get Started
        </button>
      </div>
    </div>
  );
}
