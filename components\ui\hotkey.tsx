"use client"

import React, { useEffect, useState, useRef, useCallback } from "react"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"

interface HotkeyProps {
  keys: string[]
  modifier?: boolean
  isBackgroundDark?: boolean
  triggerModifier?: boolean
  triggerKeys?: string[]
  onAction?: () => void
}

export const Hotkey: React.FC<HotkeyProps> = ({
  keys,
  modifier = false,
  isBackgroundDark = false,
  triggerModifier = false,
  triggerKeys = [],
  onAction,
}) => {
  const [displayKeys, setDisplayKeys] = useState(keys)
  const { theme } = useTheme()
  const [isClient, setIsClient] = useState(false)
  const isMacRef = useRef(false)

  type AnimationStage = 'idle' | 'pressing' | 'processing'
  const [animationStage, setAnimationStage] = useState<AnimationStage>('idle')
  const animationStageRef = useRef(animationStage) // Ref to hold current animation stage

  const pressTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const modifierActiveRef = useRef(false)
  const triggerKeyActiveRef = useRef(false)

  const keysDisplayRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })

  // Update animationStageRef whenever animationStage changes
  useEffect(() => {
    animationStageRef.current = animationStage
  }, [animationStage])

  useEffect(() => {
    setIsClient(true)
    if (typeof window !== "undefined") {
      isMacRef.current = window.navigator.userAgent.includes("Macintosh")
    }
  }, [])

  useEffect(() => {
    const modifierSymbol = isMacRef.current ? "⌘" : "⌃"
    setDisplayKeys(modifier ? [modifierSymbol, ...keys] : keys)
  }, [modifier, keys, isClient])

  useEffect(() => {
    // Measure dimensions when KeysDisplay is supposed to be visible and mounted.
    // KeysDisplay is visible when animationStage is 'idle' or 'pressing'.
    if (animationStageRef.current !== 'processing' && keysDisplayRef.current) {
      const currentWidth = keysDisplayRef.current.offsetWidth
      const currentHeight = keysDisplayRef.current.offsetHeight

      if (currentWidth > 0 && currentHeight > 0 && (currentWidth !== dimensions.width || currentHeight !== dimensions.height)) {
        setDimensions({
          width: currentWidth,
          height: currentHeight,
        })
      }
    }
  }, [displayKeys, animationStage, dimensions.width, dimensions.height]) // animationStage needed here to re-evaluate when it changes

  const resetAnimation = useCallback(() => {
    if (pressTimeoutRef.current) {
      clearTimeout(pressTimeoutRef.current)
      pressTimeoutRef.current = null
    }
    setAnimationStage('idle') // This will update animationStageRef via its effect
    modifierActiveRef.current = false
    triggerKeyActiveRef.current = false
  }, []) // setAnimationStage is stable

  useEffect(() => {
    if (!onAction || triggerKeys.length === 0 || !isClient) return

    const handleKeyDown = (event: globalThis.KeyboardEvent) => {
      if (animationStageRef.current !== 'idle') {
        return
      }

      const isModKey = event.metaKey || event.ctrlKey
      const keyLower = event.key.toLowerCase()

      let localModifierActive = modifierActiveRef.current;
      let localTriggerKeyActive = triggerKeyActiveRef.current;

      if (triggerModifier && isModKey) {
        localModifierActive = true;
      }
      if (triggerKeys.map(k => k.toLowerCase()).includes(keyLower)) {
        localTriggerKeyActive = true;
      }

      // Update refs immediately for subsequent checks within this event or by keyup
      modifierActiveRef.current = localModifierActive;
      triggerKeyActiveRef.current = localTriggerKeyActive;


      const canProceed =
        (triggerModifier ? localModifierActive : true) &&
        localTriggerKeyActive

      if (canProceed) {
        event.preventDefault()
        setAnimationStage('pressing') // This updates animationStage and animationStageRef

        pressTimeoutRef.current = setTimeout(() => {
          pressTimeoutRef.current = null

          const stillEffectivelyPressed =
            (triggerModifier ? modifierActiveRef.current : true) &&
            triggerKeyActiveRef.current

          if (stillEffectivelyPressed && animationStageRef.current === 'pressing') {
            setAnimationStage('processing')
          } else if (animationStageRef.current === 'pressing') {
            resetAnimation()
          }
        }, 200)
      }
    }

    const handleKeyUp = (event: globalThis.KeyboardEvent) => {
      const keyLower = event.key.toLowerCase()
      const isMac = isMacRef.current

      let relevantKeyReleased = false;

      if (triggerModifier) {
        if ((isMac && keyLower === 'meta' && event.metaKey === false) ||
            (!isMac && keyLower === 'control' && event.ctrlKey === false)) {
          modifierActiveRef.current = false
          relevantKeyReleased = true;
        }
      }
      if (triggerKeys.map(k => k.toLowerCase()).includes(keyLower)) {
        triggerKeyActiveRef.current = false
        relevantKeyReleased = true;
      }

      // If a relevant key was released and we were in 'pressing' stage
      if (relevantKeyReleased && animationStageRef.current === 'pressing') {
        const stillCanProceed =
          (triggerModifier ? modifierActiveRef.current : true) &&
          triggerKeyActiveRef.current

        if (!stillCanProceed) {
          resetAnimation() // This clears the timeout and resets stage
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    window.addEventListener("keyup", handleKeyUp)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("keyup", handleKeyUp)
      resetAnimation()
    }
  }, [onAction, triggerKeys, triggerModifier, isClient, resetAnimation])


  const isDark = isClient && (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches))

  const bgGradient = isDark
    ? "bg-gradient-to-bl from-transparent via-transparent to-background/20"
    : "bg-gradient-to-bl from-transparent via-transparent to-white/20"

  const ProcessingIcon = () => (
    <motion.div
      key="processing-icon"
      className="absolute inset-0 flex items-center justify-center text-purple-500"
      initial={{ opacity: 0, scale: 0.5, rotate: -30 }}
      animate={{ opacity: 1, scale: 1, rotate: 0, transition: { type: "spring", stiffness: 300, damping: 15, duration: 0.4 } }}
      exit={{ opacity: 0, scale: 0.5, rotate: 30, transition: { duration: 0.3 } }}
      onAnimationComplete={() => {
        if (onAction) {
          onAction()
        }
        // Short delay, then reset IF still in processing stage
        // This allows onAction to potentially unmount or change parent state first
        setTimeout(() => {
          if (animationStageRef.current === 'processing') {
            resetAnimation()
          }
        }, 50); // Reduced delay slightly
      }}
    >
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2V5M12 19V22M22 12H19M5 12H2M19.0711 4.92893L16.9497 7.05025M7.05025 16.9497L4.92893 19.0711M4.92893 4.92893L7.05025 7.05025M16.9497 16.9497L19.0711 19.0711" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <motion.circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        />
      </svg>
    </motion.div>
  )

  const KeysDisplay = () => (
    <motion.div
      ref={keysDisplayRef}
      key="keys-display"
      className="inline-flex items-center gap-[0.2em]"
      initial={false}
      animate={{ opacity: animationStage === 'processing' ? 0 : 1 }} // Use state for direct animation trigger
      transition={{ duration: 0.1 }}
    >
      {displayKeys.map((keyText, index) => (
        <motion.kbd
          key={`${keyText}-${index}`}
          suppressHydrationWarning
          className={cn(
            "inline-flex items-center justify-center rounded border border-border font-sans text-[0.7em] font-medium h-[1.5em] px-[0.5em]",
            bgGradient,
            "bg-[length:100%_130%] bg-[0_100%]",
            isBackgroundDark ? "text-background" : "text-foreground",
          )}
          animate={{ // Use state for direct animation trigger
            scale: animationStage === 'pressing' ? 0.90 : 1,
          }}
          transition={{ duration: 0.1, ease: "easeOut" }}
        >
          {keyText}
        </motion.kbd>
      ))}
    </motion.div>
  )

  return (
    <span
      className={cn(
        "inline-flex items-center align-middle relative",
        "min-h-[1.5em]"
      )}
      style={{
        width: animationStage === 'processing' && dimensions.width > 0 ? `${dimensions.width}px` : 'auto',
        height: animationStage === 'processing' && dimensions.height > 0 ? `${dimensions.height}px` : 'auto',
      }}
    >
      <AnimatePresence mode="wait">
        {animationStage === 'processing' ? ( // Use state for conditional rendering
          <ProcessingIcon />
        ) : (
          <KeysDisplay />
        )}
      </AnimatePresence>
    </span>
  )
}