'use client'

import { MessageLoading } from "@/components/ui/message-loading"

interface LoadingScreenProps {
  message?: string
}

export function LoadingScreen({ message = "Preparing your dashboard..." }: LoadingScreenProps) {
  return (
    <div className="fixed inset-0 flex flex-col items-center justify-center bg-background z-50">
      <div className="flex flex-col items-center justify-center space-y-4">
        <MessageLoading />
        <p className="text-foreground text-sm font-medium animate-pulse">{message}</p>
      </div>
    </div>
  )
}
