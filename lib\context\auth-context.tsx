"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import {
  mockUser,
  mockSignIn,
  mockSignOut,
  mockSignUp,
  getMockSession
} from "@/lib/mock/auth-mock";

type User = typeof mockUser;

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ user: User }>;
  signOut: () => Promise<void>;
  signUp: (email: string, password: string, name: string) => Promise<{ user: User }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Vérifier si un cookie auth existe
    const hasAuthCookie = document.cookie.includes('auth=');

    // Simuler un délai de chargement
    const timer = setTimeout(() => {
      if (hasAuthCookie) {
        const session = getMockSession();
        setUser(session.user);
      }
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const result = await mockSignIn(email, password);
      setUser(result.user);

      // Créer un cookie d'authentification (valide 7 jours)
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 7);
      document.cookie = `auth=true; expires=${expiryDate.toUTCString()}; path=/`;

      return result;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setIsLoading(true);
    try {
      await mockSignOut();
      setUser(null);

      // Supprimer le cookie d'authentification
      document.cookie = "auth=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, name: string) => {
    setIsLoading(true);
    try {
      const result = await mockSignUp(email, password, name);
      setUser(result.user);

      // Créer un cookie d'authentification (valide 7 jours)
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 7);
      document.cookie = `auth=true; expires=${expiryDate.toUTCString()}; path=/`;

      return result;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, signIn, signOut, signUp }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
