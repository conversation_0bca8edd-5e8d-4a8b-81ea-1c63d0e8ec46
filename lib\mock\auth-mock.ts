// Mock data pour l'authentification
export const mockUser = {
  id: "user-123456",
  email: "<EMAIL>",
  name: "Utilisateur Test",
  avatar: null,
  createdAt: new Date().toISOString(),
  role: "user",
};

// Mock data pour les comptes TikTok
export const mockAccounts = [
  {
    id: 1,
    username: "@tiktok_creator123",
    type: "Premium",
    status: "Actif",
    followers: 12500,
    views: 145000,
    revenue: 450,
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 jours avant
  },
  {
    id: 2,
    username: "@fashion_trends",
    type: "Pro",
    status: "Actif",
    followers: 8200,
    views: 98000,
    revenue: 320,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 jours avant
  },
  {
    id: 3,
    username: "@gaming_master",
    type: "Standard",
    status: "En préparation",
    followers: 0,
    views: 0,
    revenue: 0,
    createdAt: new Date().toISOString(), // Aujourd'hui
  },
];

// Mock data pour les statistiques
export const mockStats = {
  totalAccounts: 3,
  activeAccounts: 2,
  totalRevenue: 770,
  totalFollowers: 20700,
  totalViews: 243000,
  growthRate: 12.5,
};

// Mock data pour les transactions
export const mockTransactions = [
  {
    id: 1,
    type: "Achat",
    plan: "Premium",
    amount: 199,
    date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    status: "Complété",
  },
  {
    id: 2,
    type: "Achat",
    plan: "Pro",
    amount: 49,
    date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    status: "Complété",
  },
  {
    id: 3,
    type: "Achat",
    plan: "Standard",
    amount: 10,
    date: new Date().toISOString(),
    status: "En attente",
  },
];

// Mock data pour les abonnements
export const mockSubscription = {
  id: "sub-123456",
  type: "Premium",
  price: 29.99,
  startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
  nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
  status: "Actif",
  discount: 20, // Pourcentage de réduction
};

// Fonction pour simuler l'authentification
export function getMockSession() {
  return {
    user: mockUser,
    expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 jours à partir de maintenant
  };
}

// Fonction pour simuler la déconnexion
export function mockSignOut() {
  console.log("Utilisateur déconnecté (simulation)");
  return Promise.resolve();
}

// Fonction pour simuler la connexion
export function mockSignIn(email: string, password: string) {
  console.log(`Connexion simulée pour ${email}`);
  return Promise.resolve({ user: mockUser });
}

// Fonction pour simuler l'inscription
export function mockSignUp(email: string, password: string, name: string) {
  console.log(`Inscription simulée pour ${email}`);
  return Promise.resolve({ user: { ...mockUser, email, name } });
}
