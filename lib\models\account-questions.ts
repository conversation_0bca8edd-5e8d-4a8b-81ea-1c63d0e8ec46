import { AccountCreationQuestion, QuestionType } from './types';

/**
 * Liste des questions pour la création d'un compte TikTok
 */
export const accountCreationQuestions: AccountCreationQuestion[] = [
  {
    id: '1',
    question: 'Quel nom d\'utilisateur souhaitez-vous pour votre compte TikTok ?',
    type: QuestionType.TEXT,
    required: true
  },
  {
    id: '2',
    question: 'Quelle est la niche principale de votre compte ?',
    type: QuestionType.MULTIPLE_CHOICE,
    options: [
      'Mode et beauté',
      'Fitness et santé',
      'Voyage',
      'Cuisine',
      'Technologie',
      'Finance',
      'Éducation',
      'Divertissement',
      'Autre'
    ],
    required: true
  },
  {
    id: '3',
    question: 'Quel est votre public cible principal ?',
    type: QuestionType.MULTIPLE_CHOICE,
    options: [
      'Adolescents (13-17 ans)',
      'Jeunes adultes (18-24 ans)',
      'Adultes (25-34 ans)',
      'Adultes d\'âge moyen (35-44 ans)',
      'Seniors (45+ ans)',
      'Tous les âges'
    ],
    required: true
  },
  {
    id: '4',
    question: 'Quel type de contenu prévoyez-vous de publier ?',
    type: QuestionType.MULTIPLE_CHOICE,
    options: [
      'Tutoriels',
      'Vlogs',
      'Sketches humoristiques',
      'Danses et challenges',
      'Contenu éducatif',
      'Revues de produits',
      'Autre'
    ],
    required: true
  },
  {
    id: '5',
    question: 'Quelle stratégie de monétisation vous intéresse le plus ?',
    type: QuestionType.MULTIPLE_CHOICE,
    options: [
      'Programme Créateur TikTok',
      'Partenariats avec des marques',
      'Vente de produits',
      'Promotion d\'affiliation',
      'Redirection vers d\'autres plateformes (YouTube, Instagram)',
      'Autre'
    ],
    required: true
  },
  {
    id: '6',
    question: 'Avez-vous des exigences spécifiques pour votre compte ?',
    type: QuestionType.TEXT,
    required: false
  }
];
