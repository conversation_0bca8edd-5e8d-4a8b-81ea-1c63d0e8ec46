/**
 * Types pour l'application TikPay
 * 
 * Note: Ces types sont utilisés pour le développement.
 * TODO: À remplacer par des modèles Prisma lorsque la base de données sera configurée.
 */

// Type pour les utilisateurs
export interface User {
  id: string;
  email: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Type pour les comptes TikTok
export interface TikTokAccount {
  id: string;
  username: string;
  password: string; // Stocké de manière sécurisée
  niche: string;
  targetAudience: string;
  contentType: string;
  monetizationStrategy: string;
  isRevealed: boolean; // Si le mot de passe a été révélé à l'utilisateur
  createdAt: Date;
  updatedAt: Date;
  userId: string; // Référence à l'utilisateur qui a commandé ce compte
}

// Type pour les paiements
export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string; // Référence à l'utilisateur qui a effectué le paiement
  tikTokAccountId: string; // Référence au compte TikTok associé
}

// Statuts possibles pour un paiement
export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED'
}

// Type pour les questions du formulaire de création de compte
export interface AccountCreationQuestion {
  id: string;
  question: string;
  type: QuestionType;
  options?: string[]; // Pour les questions à choix multiples
  required: boolean;
}

// Types de questions possibles
export enum QuestionType {
  TEXT = 'TEXT',
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
  CHECKBOX = 'CHECKBOX',
  NUMBER = 'NUMBER'
}

// Type pour les réponses aux questions
export interface AccountCreationAnswer {
  questionId: string;
  answer: string | string[] | number;
  userId: string;
}

// Type pour les demandes de création de compte
export interface AccountCreationRequest {
  id: string;
  userId: string;
  answers: AccountCreationAnswer[];
  status: AccountCreationStatus;
  createdAt: Date;
  updatedAt: Date;
}

// Statuts possibles pour une demande de création de compte
export enum AccountCreationStatus {
  SUBMITTED = 'SUBMITTED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  PAID = 'PAID',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED'
}
