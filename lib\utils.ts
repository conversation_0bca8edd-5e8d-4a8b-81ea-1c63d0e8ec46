import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { redirect } from "next/navigation"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function encodedRedirect(
  type: "error" | "success",
  path: string,
  message: string
) {
  const params = new URLSearchParams()
  params.set("type", type)
  params.set("message", message)
  return redirect(`${path}?${params.toString()}`)
}
