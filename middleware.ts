import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Ce middleware est simplifié pour le développement
// En production, vous devriez vérifier la session côté serveur
export function middleware(request: NextRequest) {
  // Pour le développement, nous allons simplement vérifier si un cookie "auth" existe
  // Dans une application réelle, vous vérifieriez la validité de la session
  const authCookie = request.cookies.get('auth');
  
  // Si l'utilisateur accède à une route protégée et n'est pas connecté
  if (request.nextUrl.pathname.startsWith('/dashboard') && !authCookie) {
    // Rediriger vers la page de connexion
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  // Si l'utilisateur est déjà connecté et essaie d'accéder à la page de connexion
  if ((request.nextUrl.pathname === '/login' || request.nextUrl.pathname === '/register') && authCookie) {
    // Rediriger vers le dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
  
  return NextResponse.next();
}

// Configurer les chemins sur lesquels le middleware s'applique
export const config = {
  matcher: ['/dashboard/:path*', '/login', '/register'],
};
