{"name": "hexa-tikpay", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.0.4", "@supabase/ssr": "^0.6.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "10.16.4", "lucide-react": "^0.511.0", "motion": "^12.12.1", "next": "15.3.2", "next-themes": "^0.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-use-measure": "^2.1.7", "recharts": "^2.12.3", "sonner": "^1.4.3", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.25.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}